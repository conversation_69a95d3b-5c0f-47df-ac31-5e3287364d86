const BirdIntake = require("../model/BirdIntake");
const BirdPen = require("../model/BirdPen");
const Staff = require("../model/Staff");
const Supplier = require("../model/Supplier");
const Customer = require("../model/Customer");
const FeedType = require("../model/FeedType");
const FeedDetails = require("../model/FeedDetails");
const BirdRemoved = require("../model/BirdRemoved");
const DailyProductionRecord = require("../model/DailyProductionRecord");
const MedicationRecord = require("../model/MedicationRecord");
const SalesRecord = require("../model/SalesRecord");
const SuppliesRecord = require("../model/SuppliesRecord");

const deleteBirdIntake = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const { id } = req.params;
    const deletedRecord = await BirdIntake.deleteOne({
      _id: id,
    });
    if (deletedRecord.deletedCount > 0) {
      let message = "record deleted successfuly !!";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/deleterecord/birdintake");
    } else {
      let message = "no record with this id found";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/deleterecord/birdintake");
    }
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/deleterecord/birdintake");
  }
};

const deleteBirdPen = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const { id } = req.params;
    const deletedRecord = await BirdPen.deleteOne({
      _id: id,
    });
    if (deletedRecord.deletedCount > 0) {
      let message = "record deleted successfuly !!";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/deleterecord/birdpen");
    } else {
      let message = "no record with this id found";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/deleterecord/birdpen");
    }
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/deleterecord/birdpen");
  }
};

const deleteBirdRemove = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const { id } = req.params;
    const deletedRecord = await BirdRemoved.deleteOne({
      _id: id,
    });
    if (deletedRecord.deletedCount > 0) {
      let message = "record deleted successfuly !!";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/deleterecord/birdremove");
    } else {
      let message = "no record with this id found";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/deleterecord/birdremove");
    }
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/deleterecord/birdremove");
  }
};
const deleteFeedType = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const { id } = req.params;
    const deletedRecord = await FeedType.deleteOne({
      _id: id,
    });
    if (deletedRecord.deletedCount > 0) {
      let message = "record deleted successfuly !!";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/deleterecord/feedtype");
    } else {
      let message = "no record with this id found";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/deleterecord/feedtype");
    }
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/deleterecord/feedtype");
  }
};

const deleteFeedDetails = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const { id } = req.params;
    const deletedRecord = await FeedDetails.deleteOne({
      _id: id,
    });
    if (deletedRecord.deletedCount > 0) {
      let message = "record deleted successfuly !!";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/deleterecord/feeddetail");
    } else {
      let message = "no record with this id found";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/deleterecord/feeddetail");
    }
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/deleterecord/feeddetail");
  }
};
const deleteCustomer = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const { id } = req.params;
    const deletedRecord = await Customer.deleteOne({
      _id: id,
    });
    if (deletedRecord.deletedCount > 0) {
      let message = "record deleted successfuly !!";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/deleterecord/customer");
    } else {
      let message = "no record with this id found";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/deleterecord/customer");
    }
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/deleterecord/customer");
  }
};
const deleteStaff = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const { id } = req.params;
    const deletedRecord = await Staff.deleteOne({
      _id: id,
    });
    if (deletedRecord.deletedCount > 0) {
      let message = "record deleted successfuly !!";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/deleterecord/staff");
    } else {
      let message = "no record with this id found";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/deleterecord/staff");
    }
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/deleterecord/staff");
  }
};
const deleteSupplier = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const { id } = req.params;
    const deletedRecord = await Supplier.deleteOne({
      _id: id,
    });
    if (deletedRecord.deletedCount > 0) {
      let message = "record deleted successfuly !!";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/deleterecord/supplier");
    } else {
      let message = "no record with this id found";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/deleterecord/supplier");
    }
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/deleterecord/supplier");
  }
};
const deleteDailyProductionRecord = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const { id } = req.params;
    const deletedRecord = await DailyProductionRecord.deleteOne({
      _id: id,
    });
    if (deletedRecord.deletedCount > 0) {
      let message = "record deleted successfuly !!";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/deleterecord/dailyproductionrecord");
    } else {
      let message = "no record with this id found";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/deleterecord/dailyproductionrecord");
    }
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/deleterecord/dailyproductionrecord");
  }
};
const deleteMedicationRecord = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const { id } = req.params;
    const deletedRecord = await MedicationRecord.deleteOne({
      _id: id,
    });
    if (deletedRecord.deletedCount > 0) {
      let message = "record deleted successfuly !!";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/deleterecord/medicationrecord");
    } else {
      let message = "no record with this id found";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/deleterecord/medicationrecord");
    }
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/deleterecord/medicationrecord");
  }
};
const deleteSalesRecord = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const { id } = req.params;
    const deletedRecord = await SalesRecord.deleteOne({
      _id: id,
    });
    if (deletedRecord.deletedCount > 0) {
      let message = "record deleted successfuly !!";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/deleterecord/salesrecord");
    } else {
      let message = "no record with this id found";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/deleterecord/salesrecord");
    }
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/deleterecord/salesrecord");
  }
};
const deleteSuppliesRecord = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const { id } = req.params;
    const deletedRecord = await SuppliesRecord.deleteOne({
      _id: id,
    });
    if (deletedRecord.deletedCount > 0) {
      let message = "record deleted successfuly !!";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/deleterecord/suppliesrecord");
    } else {
      let message = "no record with this id found";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/deleterecord/suppliesrecord");
    }
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/deleterecord/suppliesrecord");
  }
};
module.exports = {
  deleteBirdIntake,
  deleteBirdPen,
  deleteBirdRemove,
  deleteFeedType,
  deleteFeedDetails,
  deleteCustomer,
  deleteStaff,
  deleteSupplier,
  deleteDailyProductionRecord,
  deleteMedicationRecord,
  deleteSalesRecord,
  deleteSuppliesRecord,
};
