const mongoose = require("mongoose");

const birdPenSchema = new mongoose.Schema(
  {
    code: {
      type: String,
      required: true,
    },
    name: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      required: false,
    },
    date: {
      type: Date,
      required: true,
    },
    no_of_cage: {
      type: Number,
      required: true,
    },
    cage_capacity: {
      type: Number,
      required: true,
    },
    stock: {
      type: Number,
      required: true,
    },
    batch: {
      type: String,
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

module.exports = mongoose.model("BirdPen", birdPenSchema);
