<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/global-styles.css" />
    <title>Daily Production Record - Farm Management System</title>
  </head>
  <body>
    <!-- Modern Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="app-logo">Farm Management System</div>
        <div class="user-info">
          <span>Daily Production Record</span>
        </div>
      </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav">
      <div class="breadcrumb-content">
        <a href="/management/homepage" class="breadcrumb-item">Dashboard</a>
        <span class="breadcrumb-separator">›</span>
        <a href="/management/homepage" class="breadcrumb-item">Records</a>
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-item active">Daily Production</span>
      </div>
    </nav>

    <!-- Main Container -->
    <div class="app-container">
      <!-- Alerts -->
      <% if(hasErr){%>
      <div class="alert alert-danger">
        <div>
          <% errMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%> <% if(hasSuccess){%>
      <div class="alert alert-success">
        <div>
          <% successMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%>

      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">📊 Daily Production Record</h1>
        <p class="page-subtitle">
          Record daily farm activities, feed consumption, and egg production
        </p>
        <div class="page-actions">
          <a href="/management/homepage" class="btn btn-secondary btn-back"
            >Back to Dashboard</a
          >
          <a href="/report/dailyproduction" class="btn btn-outline"
            >View Reports</a
          >
        </div>
      </div>
      <!-- Production Record Form -->
      <div class="form-container">
        <form
          action="/record/dailyproduction"
          method="POST"
          id="productionForm"
        >
          <!-- Farm Information Section -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">🏠 Farm Information</h3>
            </div>
            <div class="card-body">
              <div class="form-grid">
                <div class="form-group">
                  <label for="pen" class="form-label">🏠 Select Pen</label>
                  <select
                    name="pen"
                    class="form-control form-select"
                    id="pen"
                    required
                  >
                    <option value="">Choose a pen...</option>
                    <% pens.forEach(pen =>{ %>
                    <option value="<%= pen %>"><%= pen %></option>
                    <%}) %>
                  </select>
                </div>

                <div class="form-group">
                  <label for="mortality" class="form-label"
                    >💀 Mortality Count</label
                  >
                  <input
                    type="number"
                    class="form-control"
                    name="mortality"
                    id="mortality"
                    placeholder="Number of birds that died today"
                    min="0"
                    value="0"
                    required
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Feed Consumption Section -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">🌾 Feed Consumption</h3>
            </div>
            <div class="card-body">
              <div class="form-grid">
                <div class="form-group">
                  <label for="feed_code" class="form-label">🏷️ Feed Code</label>
                  <select
                    name="feed_code"
                    class="form-control form-select"
                    id="feed_code"
                    required
                  >
                    <option value="">Select feed type...</option>
                    <% feedCode.forEach(feed =>{ %>
                    <option value="<%= feed %>"><%= feed %></option>
                    <%}) %>
                  </select>
                </div>

                <div class="form-group">
                  <label for="feed_unit" class="form-label">📏 Feed Unit</label>
                  <select
                    name="feed_unit"
                    class="form-control form-select"
                    id="feed_unit"
                    required
                  >
                    <option value="">Select unit...</option>
                    <option value="bag">Bag</option>
                    <option value="boxes">Boxes</option>
                    <option value="bottles">Bottles</option>
                    <option value="pieces">Pieces</option>
                    <option value="sachets">Sachets</option>
                  </select>
                </div>

                <div class="form-group">
                  <label for="feed_qty" class="form-label"
                    >🔢 Feed Quantity</label
                  >
                  <input
                    type="number"
                    class="form-control"
                    name="feed_qty"
                    id="feed_qty"
                    placeholder="Enter quantity consumed"
                    min="0"
                    step="0.1"
                    required
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Egg Production Section -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">🥚 Egg Production</h3>
            </div>
            <div class="card-body">
              <div class="form-grid">
                <div class="form-group">
                  <label for="big_Eggs" class="form-label"
                    >🥚 Big Eggs (Crates)</label
                  >
                  <input
                    type="number"
                    class="form-control"
                    name="big_Eggs"
                    id="big_Eggs"
                    placeholder="Number of big egg crates"
                    min="0"
                    value="0"
                    required
                  />
                </div>
                <div class="form-group">
                  <label for="small" class="form-label"
                    >🥚 Small Eggs (Crates)</label
                  >
                  <input
                    type="number"
                    class="form-control"
                    name="small_egg"
                    id="small"
                    placeholder="Number of small egg crates"
                    min="0"
                    value="0"
                    required
                  />
                </div>
                <div class="form-group">
                  <label for="egg_Pieces" class="form-label"
                    >🥚 broken egg crate</label
                  >
                  <input
                    type="number"
                    class="form-control"
                    name="broken_eggs_crate"
                    id="broken_eggs_crate"
                    placeholder="Individual egg pieces"
                    min="0"
                    value="0"
                    required
                  />
                </div>
                <div class="form-group">
                  <label for="big_Eggs" class="form-label"
                    >🥚 Big Eggs Crates(Price)
                  </label>
                  <input
                    type="number"
                    class="form-control"
                    name="b_egg_price"
                    id="b_egg_price"
                    placeholder="Number of big egg crates"
                    min="0"
                    value="0"
                    required
                  />
                </div>
                <div class="form-group">
                  <label for="unsorted_Eggs" class="form-label"
                    >🥚 small Eggs Crates (Price)</label
                  >
                  <input
                    type="number"
                    class="form-control"
                    name="s_egg_price"
                    id="s_egg_price"
                    placeholder="Number of unsorted egg crates"
                    min="0"
                    value="0"
                    required
                  />
                </div>
                <div class="form-group">
                  <label for="egg_Pieces" class="form-label"
                    >🥚 broken egg price</label
                  >
                  <input type="number" class="form-control"
                  name="broken_egg_price" id="broken_egg_price"
                  placeholder="Individual egg pieces" min="0" value="0" required
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Production Summary -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">📊 Production Summary</h3>
            </div>
            <div class="card-body">
              <div class="grid grid-3">
                <div class="text-center">
                  <h4 class="text-primary" id="totalCrates">0</h4>
                  <p class="text-muted">Total Crates</p>
                </div>
                <div class="text-center">
                  <h4 class="text-success" id="totalValue">₦0.00</h4>
                  <p class="text-muted">Total Value</p>
                </div>
                <div class="text-center">
                  <h4 class="text-info" id="productionPercent">0%</h4>
                  <p class="text-muted">Est. Production %</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="d-flex justify-between align-center">
            <div>
              <span class="text-muted">All fields are required</span>
            </div>
            <div class="d-flex gap-2">
              <button type="reset" class="btn btn-secondary">
                🔄 Reset Form
              </button>
              <button type="submit" class="btn btn-success btn-save">
                💾 Save Production Record
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Enhanced JavaScript for real-time calculations -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const bigEggsInput = document.getElementById("big_Eggs");
        const smallEggsInput = document.getElementById("small");
        const brokenEggsInput = document.getElementById("broken_eggs_crate");
        const bigEggPriceInput = document.getElementById("b_egg_price");
        const smallEggPriceInput = document.getElementById("s_egg_price");
        const brokenEggPriceInput = document.getElementById("broken_egg_price");

        const totalCratesDisplay = document.getElementById("totalCrates");
        const totalValueDisplay = document.getElementById("totalValue");
        const productionPercentDisplay = document.getElementById("productionPercent");

        function calculateTotals() {
          const bigEggs = parseFloat(bigEggsInput.value) || 0;
          const smallEggs = parseFloat(smallEggsInput.value) || 0;
          const brokenEggs = parseFloat(brokenEggsInput.value) || 0;
          const bigEggPrice = parseFloat(bigEggPriceInput.value) || 0;
          const smallEggPrice = parseFloat(smallEggPriceInput.value) || 0;
          const brokenEggPrice = parseFloat(brokenEggPriceInput.value) || 0;

          // Calculate totals
          const totalCrates = bigEggs + smallEggs + brokenEggs;
          const bigEggTotal = bigEggs * 30 * bigEggPrice;
          const smallEggTotal = smallEggs * 30 * smallEggPrice;
          const brokenEggTotal = brokenEggs * 30 * brokenEggPrice;
          const totalValue = bigEggTotal + smallEggTotal + brokenEggTotal;

          // Estimate production percentage (assuming 1000 birds per pen)
          const estimatedBirds = 1000; // This should come from pen data if available
          const totalEggsProduced = (bigEggs + smallEggs + brokenEggs) * 30;
          const productionPercent = estimatedBirds > 0 ? (totalEggsProduced / estimatedBirds) * 100 : 0;

          // Update displays
          totalCratesDisplay.textContent = totalCrates.toFixed(1);
          totalValueDisplay.textContent = new Intl.NumberFormat("en-NG", {
            style: "currency",
            currency: "NGN",
          }).format(totalValue);
          productionPercentDisplay.textContent = Math.min(productionPercent, 120).toFixed(1) + "%";
        }

        // Add event listeners
        [
          bigEggsInput,
          smallEggsInput,
          brokenEggsInput,
          bigEggPriceInput,
          smallEggPriceInput,
          brokenEggPriceInput,
        ].forEach((input) => {
          input.addEventListener("input", calculateTotals);
        });

        // Form submission enhancement
        const form = document.getElementById("productionForm");
        form.addEventListener("submit", function (e) {
          const submitBtn = form.querySelector('button[type="submit"]');
          submitBtn.innerHTML = '<span class="loading"></span> Saving...';
          submitBtn.disabled = true;
        });

        // Initialize calculations
        calculateTotals();
      });
    </script>
  </body>
</html>
