{"name": "farm_product_application", "version": "1.0.0", "description": "farm product management", "main": "index.js", "scripts": {"dev": "nodemon index.js"}, "author": "", "license": "ISC", "dependencies": {"argon2": "^0.40.3", "connect-flash": "^0.1.1", "connect-mongo": "^5.1.0", "connect-redis": "^7.1.1", "dotenv": "^16.4.5", "ejs": "^3.1.10", "express": "^4.19.2", "express-session": "^1.18.0", "ioredis": "^5.4.1", "mongoose": "^8.15.1", "passport": "^0.7.0", "passport-local": "^1.0.0"}, "devDependencies": {"nodemon": "^3.1.4"}}