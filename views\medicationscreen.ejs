<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/global-styles.css" />
    <title>Medication Record - Farm Management System</title>
  </head>
  <body>
    <!-- Modern Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="app-logo">Farm Management System</div>
        <div class="user-info">
          <span>Medication Record</span>
        </div>
      </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav">
      <div class="breadcrumb-content">
        <a href="/management/homepage" class="breadcrumb-item">Dashboard</a>
        <span class="breadcrumb-separator">›</span>
        <a href="/management/homepage" class="breadcrumb-item">Records</a>
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-item active">Medication Record</span>
      </div>
    </nav>

    <!-- Main Container -->
    <div class="app-container">
      <!-- Alerts -->
      <% if(hasErr){%>
      <div class="alert alert-danger">
        <div>
          <% errMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%> <% if(hasSuccess){%>
      <div class="alert alert-success">
        <div>
          <% successMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%>

      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">💊 Medication Record</h1>
        <p class="page-subtitle">
          Record medication administration and veterinary treatments
        </p>
        <div class="page-actions">
          <a href="/management/homepage" class="btn btn-secondary btn-back"
            >Back to Dashboard</a
          >
          <a href="/report/medication" class="btn btn-outline"
            >View Medication Reports</a
          >
        </div>
      </div>
      <!-- Medication Record Form -->
      <div class="form-container">
        <form action="/record/medication" method="POST" id="medicationForm">
          <!-- Treatment Location -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">🏠 Treatment Location</h3>
            </div>
            <div class="card-body">
              <div class="form-group">
                <label for="pen" class="form-label">🏠 Select Pen</label>
                <select
                  name="pen"
                  class="form-control form-select"
                  id="pen"
                  required
                >
                  <option value="">Choose pen to treat...</option>
                  <% pens.forEach(pen =>{ %>
                  <option value="<%= pen %>"><%= pen %></option>
                  <%}) %>
                </select>
              </div>
            </div>
          </div>

          <!-- Medication Details -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">💊 Medication Details</h3>
            </div>
            <div class="card-body">
              <div class="form-grid">
                <div class="form-group" style="grid-column: 1 / -1">
                  <label for="medication_desc" class="form-label"
                    >📝 Treatment Description</label
                  >
                  <textarea
                    class="form-control"
                    name="medication_desc"
                    id="medication_desc"
                    placeholder="Describe the condition being treated and treatment plan"
                    rows="3"
                    required
                  ></textarea>
                </div>

                <div class="form-group">
                  <label for="drug" class="form-label"
                    >💊 Drug/Medicine Name</label
                  >
                  <input
                    type="text"
                    class="form-control"
                    name="drug"
                    id="drug"
                    placeholder="Enter drug or medicine name"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="quantity" class="form-label"
                    >📏 Quantity/Dosage</label
                  >
                  <input
                    type="text"
                    class="form-control"
                    name="quantity"
                    id="quantity"
                    placeholder="Enter quantity and unit (e.g., 500ml, 10 tablets)"
                    required
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Supplier & Cost -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">🏭 Supplier & Cost</h3>
            </div>
            <div class="card-body">
              <div class="form-grid">
                <div class="form-group">
                  <label for="supplier" class="form-label"
                    >🏪 Supplier/Pharmacy</label
                  >
                  <input
                    type="text"
                    class="form-control"
                    name="supplier"
                    id="supplier"
                    placeholder="Enter supplier or pharmacy name"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="price" class="form-label"
                    >💰 Total Cost (₦)</label
                  >
                  <input
                    type="number"
                    class="form-control"
                    name="price"
                    id="price"
                    placeholder="Enter total cost of medication"
                    min="0"
                    step="0.01"
                    required
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Treatment Summary -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">📊 Treatment Summary</h3>
            </div>
            <div class="card-body">
              <div class="grid grid-3">
                <div class="text-center">
                  <h4 class="text-info" id="selectedPen">-</h4>
                  <p class="text-muted">Pen to Treat</p>
                </div>
                <div class="text-center">
                  <h4 class="text-primary" id="drugName">-</h4>
                  <p class="text-muted">Medication</p>
                </div>
                <div class="text-center">
                  <h4 class="text-success" id="totalCost">₦0.00</h4>
                  <p class="text-muted">Total Cost</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="d-flex justify-between align-center">
            <div>
              <span class="text-muted">All fields are required</span>
            </div>
            <div class="d-flex gap-2">
              <button type="reset" class="btn btn-secondary">
                🔄 Reset Form
              </button>
              <button type="submit" class="btn btn-success btn-save">
                💊 Record Medication
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Enhanced JavaScript -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const penSelect = document.getElementById("pen");
        const drugInput = document.getElementById("drug");
        const priceInput = document.getElementById("price");

        const selectedPenDisplay = document.getElementById("selectedPen");
        const drugNameDisplay = document.getElementById("drugName");
        const totalCostDisplay = document.getElementById("totalCost");

        function updateDisplays() {
          const pen = penSelect.value || "-";
          const drug = drugInput.value || "-";
          const price = parseFloat(priceInput.value) || 0;

          selectedPenDisplay.textContent = pen;
          drugNameDisplay.textContent = drug;
          totalCostDisplay.textContent = new Intl.NumberFormat("en-NG", {
            style: "currency",
            currency: "NGN",
          }).format(price);
        }

        // Add event listeners
        [penSelect, drugInput, priceInput].forEach((input) => {
          input.addEventListener("input", updateDisplays);
          input.addEventListener("change", updateDisplays);
        });

        // Form submission enhancement
        const form = document.getElementById("medicationForm");
        form.addEventListener("submit", function (e) {
          const submitBtn = form.querySelector('button[type="submit"]');
          submitBtn.innerHTML = '<span class="loading"></span> Recording...';
          submitBtn.disabled = true;
        });

        // Initialize displays
        updateDisplays();
      });
    </script>
  </body>
</html>
