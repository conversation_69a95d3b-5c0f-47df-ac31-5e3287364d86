<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/global-styles.css" />
    <title>Create Bird Intake - Farm Management System</title>
  </head>
  <body>
    <!-- Modern Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="app-logo">Farm Management System</div>
        <div class="user-info">
          <span>Create Bird Intake</span>
        </div>
      </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav">
      <div class="breadcrumb-content">
        <a href="/management/homepage" class="breadcrumb-item">Dashboard</a>
        <span class="breadcrumb-separator">›</span>
        <a href="/management/homepage" class="breadcrumb-item">Management</a>
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-item active">Create Bird Intake</span>
      </div>
    </nav>

    <!-- Main Container -->
    <div class="app-container">
      <!-- Alerts -->
      <% if(hasErr){%>
      <div class="alert alert-danger">
        <div>
          <% errMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%> <% if(hasSuccess){%>
      <div class="alert alert-success">
        <div>
          <% successMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%>

      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">🐔 Create Bird Intake Record</h1>
        <p class="page-subtitle">Add new birds to your farm inventory</p>
        <div class="page-actions">
          <a href="/management/homepage" class="btn btn-secondary btn-back"
            >Back to Dashboard</a
          >
          <a href="/management/check-birdintake" class="btn btn-outline"
            >View All Intakes</a
          >
        </div>
      </div>
      <!-- Form Container -->
      <div class="form-container">
        <form action="/management/birdintake" method="POST" id="birdIntakeForm">
          <div class="form-grid">
            <!-- Batch Code Section -->
            <div class="form-group">
              <label for="type_select" class="form-label"
                >📋 Batch Code Prefix</label
              >
              <select
                name="type_select"
                class="form-control form-select"
                id="type_select"
                required
              >
                <option value="">Select prefix...</option>
                <option value="L">L - Layer Birds</option>
                <option value="B">B - Broiler Birds</option>
                <option value="C">C - Cockerel Birds</option>
                <option value="N">N - Native Birds</option>
              </select>
            </div>

            <!-- Quantity -->
            <div class="form-group">
              <label for="qty" class="form-label">🔢 Batch Quantity</label>
              <input
                type="number"
                class="form-control"
                name="qty"
                id="qty"
                placeholder="Enter batch quantity"
                min="1"
                required
              />
            </div>

            <!-- Supplier Batch -->
            <div class="form-group">
              <label for="supplier_batch" class="form-label"
                >🏭 Supplier Batch Code</label
              >
              <select
                name="supplier_batch"
                class="form-control form-select"
                id="supplier_batch"
                required
              >
                <option value="">Select supplier batch...</option>
                <option value="CHI">CHI - Chi Farms</option>
                <option value="OBA">OBA - Oba Poultry</option>
                <option value="ANC">ANC - Anchor Farms</option>
                <option value="FSI">FSI - Farm Solutions Inc</option>
              </select>
            </div>

            <!-- Bird Type -->
            <div class="form-group">
              <label for="type" class="form-label">🐔 Bird Type</label>
              <select
                name="type"
                class="form-control form-select"
                id="type"
                required
              >
                <option value="">Select bird type...</option>
                <% birdType.forEach(type =>{ %>
                <option value="<%= type %>"><%= type %></option>
                <%}) %>
              </select>
            </div>

            <!-- Date Taken -->
            <div class="form-group">
              <label for="date_taken" class="form-label"
                >📅 Date Received</label
              >
              <input
                type="date"
                class="form-control"
                name="date_taken"
                id="date_taken"
                required
              />
            </div>

            <!-- Number of Birds -->
            <div class="form-group">
              <label for="stock" class="form-label">🐣 Number of Birds</label>
              <input
                type="number"
                class="form-control"
                name="stock"
                id="stock"
                placeholder="Enter total number of birds"
                min="1"
                required
              />
            </div>

            <!-- Supplier -->
            <div class="form-group">
              <label for="supplier" class="form-label">🚚 Supplier</label>
              <select
                name="supplier"
                class="form-control form-select"
                id="supplier"
                required
              >
                <option value="">Select supplier...</option>
                <option value="CHI">CHI - Chi Farms Limited</option>
                <option value="OBA">OBA - Oba Poultry Farms</option>
                <option value="ANC">ANC - Anchor Farms Nigeria</option>
                <option value="FSI">FSI - Farm Solutions Inc</option>
              </select>
            </div>

            <!-- Pen Assignment -->
            <div class="form-group">
              <label for="pen" class="form-label">🏠 Assign to Pen</label>
              <select
                name="pen"
                class="form-control form-select"
                id="pen"
                required
              >
                <option value="">Select pen...</option>
                <% pens.forEach(pen =>{ %>
                <option value="<%= pen %>"><%= pen %></option>
                <%}) %>
              </select>
            </div>

            <!-- Price per Bird -->
            <div class="form-group">
              <label for="price" class="form-label"
                >💰 Price per Bird (₦)</label
              >
              <input
                type="number"
                class="form-control"
                name="price"
                id="price"
                placeholder="Enter price per bird"
                min="0"
                step="0.01"
                required
              />
            </div>

            <!-- Bird Level/Stage -->
            <div class="form-group">
              <label for="level" class="form-label">📊 Bird Stage</label>
              <select
                name="level"
                class="form-control form-select"
                id="level"
                required
              >
                <option value="">Select bird stage...</option>
                <option value="DOC">DOC - Day Old Chicks</option>
                <option value="POC">POC - Point of Cage</option>
                <option value="POL">POL - Point of Lay</option>
              </select>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="d-flex justify-between align-center mt-4">
            <div>
              <span class="text-muted">Total Cost: </span>
              <strong id="totalCost" class="text-primary">₦0.00</strong>
            </div>
            <div class="d-flex gap-2">
              <button type="reset" class="btn btn-secondary">
                🔄 Reset Form
              </button>
              <button type="submit" class="btn btn-success btn-save">
                💾 Create Bird Intake
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Enhanced JavaScript for form interactions -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const stockInput = document.getElementById("stock");
        const priceInput = document.getElementById("price");
        const totalCostDisplay = document.getElementById("totalCost");
        const dateTakenInput = document.getElementById("date_taken");

        // Set today's date as default
        dateTakenInput.value = new Date().toISOString().split("T")[0];

        // Calculate total cost
        function calculateTotalCost() {
          const stock = parseFloat(stockInput.value) || 0;
          const price = parseFloat(priceInput.value) || 0;
          const total = stock * price;

          totalCostDisplay.textContent = new Intl.NumberFormat("en-NG", {
            style: "currency",
            currency: "NGN",
          }).format(total);
        }

        // Add event listeners for real-time calculation
        stockInput.addEventListener("input", calculateTotalCost);
        priceInput.addEventListener("input", calculateTotalCost);

        // Form validation and enhancement
        const form = document.getElementById("birdIntakeForm");
        form.addEventListener("submit", function (e) {
          const submitBtn = form.querySelector('button[type="submit"]');
          submitBtn.innerHTML = '<span class="loading"></span> Creating...';
          submitBtn.disabled = true;
        });

        // Add visual feedback for form fields
        const formControls = document.querySelectorAll(".form-control");
        formControls.forEach((control) => {
          control.addEventListener("focus", function () {
            this.parentElement.style.transform = "scale(1.02)";
            this.parentElement.style.transition = "transform 0.2s ease";
          });

          control.addEventListener("blur", function () {
            this.parentElement.style.transform = "scale(1)";
          });
        });
      });
    </script>
  </body>
</html>
