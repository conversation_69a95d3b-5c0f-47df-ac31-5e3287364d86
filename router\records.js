const express = require("express");
const recordsController = require("../controller/records");
const { getAllFeedCode, getAllBirdPen } = require("../config/tools");
const router = express.Router();

router.get("/supplies", async (req, res) => {
  let feedTypeDetails = await getAllFeedCode();
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("suppliesrecord", {
    successMsg,
    errMsg,
    feedType: feedTypeDetails,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
  });
});

router.get("/sales", (req, res) => {
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("salesscreen", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
  });
});

router.get("/medication", async (req, res) => {
  const pens = await getAllBirdPen();
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("medicationscreen", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
    pens,
  });
});
router.get("/dailyproduction", async (req, res) => {
  let feedCode = await getAllFeedCode();
  let pens = await getAllBirdPen();
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("dailyproductionscreen", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
    feedCode,
    pens,
  });
});
router.get(
  "/alldeleteddailyproduct",
  recordsController.getAllDeletedDailyProductionRecords
);
router.get("/allproductionrecord", recordsController.getAllProductionRecord);

router.post("/dailyproduction", recordsController.createDailyProductionRecord);

router.post("/medication", recordsController.createMedicationRecord);

router.get("/allmedicationrecord", recordsController.getAllMedicationRecord);

router.post("/sales", recordsController.createSalesRecord);

router.get("/allsalesrecord", recordsController.getAllSalesRecord);

router.get("/allsuppliesrecord", recordsController.getAllSuppliesRecord);

router.post("/supplies", recordsController.createSuppliesRecord);

router.post("/deletedailyproduction", recordsController.deleteDailyProduction);

router.post("/deletemedication", recordsController.deleteMedicationRecord);

router.post("/deletesupplies", recordsController.deleteSuppliesRecord);

router.post("/deletesales", recordsController.deleteSalesRecord);

module.exports = router;
