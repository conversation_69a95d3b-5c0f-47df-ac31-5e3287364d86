const FeedType = require("../model/FeedType");
const BirdPen = require("../model/BirdPen");

const getAllFeedType = async () => {
  try {
    const foundFeedType = await FeedType.find();
    let result = [];
    foundFeedType.forEach((feed) => {
      result.push(feed.type);
    });
    return result;
  } catch (err) {
    throw new Error(err.message);
  }
};

const getAllBirdPen = async () => {
  try {
    const foundPen = await BirdPen.find();
    let result = [];
    foundPen.forEach((pen) => {
      result.push(pen.code);
    });
    return result;
  } catch (err) {
    throw new Error(err);
  }
};

const getAllFeedCode = async () => {
  try {
    const foundFeedType = await FeedType.find();
    let result = [];
    foundFeedType.forEach((feed) => {
      result.push(feed.code);
    });
    return result;
  } catch (err) {
    throw new Error(err.message);
  }
};
// const reorderList = async () => {
//   let status = false;
//   let reorderMsg = [];
//   const feed_details = await FeedDetails.find();
//   feed_details.forEach((feed) => {
//     console.log("rer:", feed.reorder_L, feed.stock);
//     if (feed.reorder_L >= feed.stock) {
//       status = true;
//       reorderMsg.push(`feed ${feed.code} as reached reorder level !!!`);
//     } else {
//       status = false;
//       reorderMsg = [];
//     }
//   });

//   return {
//     status,
//     reorderMsg,
//   };
// };
module.exports = {
  getAllFeedType,
  getAllBirdPen,
  getAllFeedCode,
  // reorderList,
};
