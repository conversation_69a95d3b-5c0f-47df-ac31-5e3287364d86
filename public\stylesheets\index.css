/* General Styles */
body {
  font-family: "Arial", sans-serif;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  background-color: #f9f9f9;
  color: #333;
}

header {
  background-color: #4caf50;
  color: white;
  padding: 10px 20px;
  text-align: center;
  font-size: 24px;
  font-weight: bold;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Welcome Note */
.welcome.note {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  color: #4caf50;
  margin-top: 20px;
  margin-bottom: 30px;
  animation: fadeIn 2s ease-in-out;
}

/* Animations */
@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Alerts */
.alert {
  margin: 20px auto;
  padding: 15px;
  max-width: 800px;
  border-radius: 5px;
  font-size: 14px;
  text-align: center;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  animation: slideIn 1s ease-in-out;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
  animation: slideIn 1s ease-in-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-50%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

p.error,
p.success {
  margin: 5px 0;
}

/* Main Content */
main {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.menu-container {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 20px 0;
}

.btn {
  padding: 12px 24px;
  font-size: 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  background-color: #4caf50;
  color: white;
  transition: all 0.3s ease-in-out;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn:hover {
  background-color: #45a049;
  transform: scale(1.05);
}

/* Menus */
.management-menu-display,
.records-menu-display,
.report-menu-display {
  display: none;
  margin: 20px 0;
  padding: 15px;
  border: 1px solid #ccc;
  background-color: white;
  border-radius: 5px;
  animation: growIn 0.5s ease-in-out;
}

@keyframes growIn {
  from {
    transform: scaleY(0);
    opacity: 0;
  }
  to {
    transform: scaleY(1);
    opacity: 1;
  }
}

a {
  display: block;
  margin: 10px 0;
  font-size: 16px;
  color: #4caf50;
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: #3e8e41;
}

/* Manager Section */
.manager-section {
  margin-top: 20px;
  padding-top: 10px;
  border-top: 1px solid #ccc;
}

.manager-section h6 {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

/* Special Buttons */
.delete_daily_production {
  background-color: #f44336;
  color: white;
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.delete_daily_production:hover {
  background-color: #e53935;
  transform: scale(1.05);
}

/* Responsive Design */
@media (max-width: 768px) {
  .menu-container {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    margin-bottom: 10px;
  }
}
