const refreshBtn = document.getElementById("refreshBtn");
const dataTableBody = document.getElementById("data-table-body");
const printBtn = document.getElementById("printbtn");
const searchDiv = document.getElementById("searchdiv");

// Function to load staff data
function loadStaffData() {
  fetch("/management/getallstaff")
    .then((res) => res.json())
    .then((data) => {
      let htmlContent = "";
      if (data && data.length > 0) {
        data.forEach((record) => {
          const dateFormatted = record.createdAt
            ? new Date(record.createdAt).toLocaleDateString()
            : "N/A";
          // Color code based on staff level
          let levelColor = "";
          if (record.level === "director") {
            levelColor = ' style="background-color: #e3f2fd; color: #1565c0;"';
          } else if (record.level === "manager") {
            levelColor = ' style="background-color: #f3e5f5; color: #7b1fa2;"';
          } else if (record.level === "supervisor") {
            levelColor = ' style="background-color: #fff3e0; color: #ef6c00;"';
          }

          htmlContent += `
            <tr${levelColor}>
              <td>${record.staff_no}</td>
              <td>${record.name}</td>
              <td>${record.level}</td>
              <td>${record.address}</td>
              <td>${record.account_no}</td>
              <td>${record.bank}</td>
              <td>${record.expected_salary || "-"}</td>
              <td>${record.guarantor}</td>
              <td>${dateFormatted}</td>
            </tr>
          `;
        });
      } else {
        htmlContent = `
          <tr>
            <td colspan="9" style="text-align: center; padding: 20px;">
              No staff records found
            </td>
          </tr>
        `;
      }
      dataTableBody.innerHTML = htmlContent;
    })
    .catch((error) => {
      console.error("Error loading staff data:", error);
      dataTableBody.innerHTML = `
        <tr>
          <td colspan="9" style="text-align: center; padding: 20px; color: red;">
            Error loading data. Please try again.
          </td>
        </tr>
      `;
    });
}

// Load data when page loads
document.addEventListener("DOMContentLoaded", loadStaffData);

// Refresh button event listener
refreshBtn.addEventListener("click", (e) => {
  e.preventDefault();
  loadStaffData();
});

// Print button event listener
printBtn.addEventListener("click", (e) => {
  e.preventDefault();
  searchDiv.style.display = "none";
  printBtn.style.display = "none";
  window.print();
  // Restore elements after printing
  setTimeout(() => {
    searchDiv.style.display = "block";
    printBtn.style.display = "block";
  }, 1000);
});
