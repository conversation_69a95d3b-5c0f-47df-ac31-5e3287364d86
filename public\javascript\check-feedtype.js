const refreshBtn = document.getElementById("refreshBtn");
const dataTableBody = document.getElementById("data-table-body");
const printBtn = document.getElementById("printbtn");
const searchDiv = document.getElementById("searchdiv");

// Function to load feed type data
function loadFeedTypeData() {
  fetch("/management/allfeedtype")
    .then((res) => res.json())
    .then((data) => {
      let htmlContent = "";
      if (data && data.length > 0) {
        data.forEach((record) => {
          const dateFormatted = record.createdAt ? new Date(record.createdAt).toLocaleDateString() : 'N/A';
          htmlContent += `
            <tr>
              <td>${record.code}</td>
              <td>${record.type}</td>
              <td>${record.Disc}</td>
              <td>${dateFormatted}</td>
            </tr>
          `;
        });
      } else {
        htmlContent = `
          <tr>
            <td colspan="4" style="text-align: center; padding: 20px;">
              No feed type records found
            </td>
          </tr>
        `;
      }
      dataTableBody.innerHTML = htmlContent;
    })
    .catch((error) => {
      console.error("Error loading feed type data:", error);
      dataTableBody.innerHTML = `
        <tr>
          <td colspan="4" style="text-align: center; padding: 20px; color: red;">
            Error loading data. Please try again.
          </td>
        </tr>
      `;
    });
}

// Load data when page loads
document.addEventListener("DOMContentLoaded", loadFeedTypeData);

// Refresh button event listener
refreshBtn.addEventListener("click", (e) => {
  e.preventDefault();
  loadFeedTypeData();
});

// Print button event listener
printBtn.addEventListener("click", (e) => {
  e.preventDefault();
  searchDiv.style.display = "none";
  printBtn.style.display = "none";
  window.print();
  // Restore elements after printing
  setTimeout(() => {
    searchDiv.style.display = "block";
    printBtn.style.display = "block";
  }, 1000);
});
