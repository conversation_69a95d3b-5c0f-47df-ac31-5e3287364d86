const fromInput = document.getElementById("from");
const toInput = document.getElementById("to");
const submitBtn = document.getElementById("submit");
const dateFilterForm = document.getElementById("dateFilterForm");
const recordContainer = document.getElementById("record-container");

// Listen for filter form submit
if (dateFilterForm) {
  dateFilterForm.addEventListener("submit", function (e) {
    e.preventDefault();
    let payload = {
      date_from: fromInput.value,
      date_to: toInput.value,
    };
    // Fetch filtered records from the server
    fetch("/report/sales", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    })
      .then((res) => res.json())
      .then((data) => {
        // Build filtered record rows
        let html = `
          <div class='record-header' id='record-header'>
                <p>Date</p>
                <p>Customer</p>
                <p>Big Egg</p>
                <p>Big Egg P</p>
                <p>Small Egg</p>
                <p>Small Egg P</p>
                <p>Broken Egg</p>
                <p>Broken Egg P</p>
                <p>Total Amount</p>
                <p>Remarks</p>
                <p>Staff Name</p>
                <p>Staff No</p>
                <span style="width: 48px"></span>
          </div>
        `;
        if (data.length > 0) {
          data.forEach(function (record) {
            html += `
              <div class='record-row'>
                <p>${new Date(record.date).toLocaleDateString()}</p>
                <p>${record.customer}</p>                 
                <p>${record.big_egg_crate}</p>                 
                <p>${record.big_egg_price}</p>                 
                <p>${record.small_egg_crate}</p>                 
                <p>${record.small_egg_price}</p>                 
                <p>${record.broken_egg_crate}</p>                 
                <p>${record.broken_egg_price}</p>                 
                <p>${record.total_price}</p>                 
                <p>${record.remark}</p> 
                <p>${record.staff_name} </p>
                <p>${record.staff_no} </p>
                 <a href="/deleterecord/salesrecord/${
                   record._id
                 }" >               
          <i class="material-icons" style="font-size: 32px; color: red" >delete</i>                 
              </div>                 
            `;
          });
        } else {
          html += `<p style='text-align:center'>no record found</p>`;
        }
        recordContainer.innerHTML = html;
      })
      .catch((err) => {
        recordContainer.innerHTML = `<p style='text-align:center;color:red'>Error loading records</p>`;
      });
  });
}
