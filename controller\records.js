const managementService = require("../service/management");
const SuppliesRecord = require("../model/SuppliesRecord");
const SalesRecord = require("../model/SalesRecord");
const MedicationRecord = require("../model/MedicationRecord");
const DailyProductionRecord = require("../model/DailyProductionRecord");
const FeedDetails = require("../model/FeedDetails");
const FeedType = require("../model/FeedType");
const BirdPen = require("../model/BirdPen");
const DeletedDailyRecord = require("../model/DeletedDailyRecord");

const createSuppliesRecord = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const { supplier, feedtype, price, quantity, remark, feed_unit } = req.body;
    const user = req.user;
    const amount = price * quantity;
    let date = new Date();
    const foundFeedDetails = await FeedDetails.findOne({
      code: feedtype,
      unit: feed_unit,
    });
    if (!foundFeedDetails) {
      let message = "no feed details found with this code and unit";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/record/supplies");
    }
    let stock_bf = foundFeedDetails.stock;
    let stock_cf = Number(foundFeedDetails.stock) + Number(quantity);
    const createdSupplies = await managementService.createSuppliesRecord(
      date,
      supplier,
      feedtype,
      feed_unit,
      stock_bf,
      price,
      quantity,
      amount,
      remark,
      stock_cf,
      user.name,
      user.staff_no
    );
    if (createdSupplies.date) {
      let message = "supplies record  created successfuly";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/record/supplies");
    } else {
      let message = "something went wrong try again later";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/record/supplies");
    }
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/record/supplies");
  }
};

const getAllSuppliesRecord = async (req, res) => {
  try {
    const allSupplies = await SuppliesRecord.find();
    return res.json(allSupplies);
  } catch (err) {
    console.log(err);
    return res.json(err);
  }
};

const createSalesRecord = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const {
      customer,
      big_egg_crate,
      big_egg_price,
      small_egg_crate,
      small_egg_price,
      broken_egg_crate,
      broken_egg_price,
      remark,
    } = req.body;
    const user = req.user;
    const date = new Date();
    const totalPrice = () => {
      let bigEggPrice = Number(big_egg_crate) * Number(big_egg_price);
      let smallEggPrice = Number(small_egg_crate) * Number(small_egg_price);
      let brokenEggPrice = Number(broken_egg_crate) * Number(broken_egg_price);
      return bigEggPrice + smallEggPrice + brokenEggPrice;
    };
    const createdSales = await managementService.createSalesRecord(
      date,
      customer,
      big_egg_crate,
      big_egg_price,
      small_egg_crate,
      small_egg_price,
      broken_egg_crate,
      broken_egg_price,
      totalPrice(),
      remark,
      user.name,
      user.staff_no
    );
    if (createdSales.date) {
      let message = "sales record  created successfuly";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/record/sales");
    } else {
      let message = "something went wrong try again later";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/record/sales");
    }
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/record/sales");
  }
};

const getAllSalesRecord = async (req, res) => {
  try {
    const allSalesRecord = await SalesRecord.find();
    return res.json(allSalesRecord);
  } catch (err) {
    console.log(err);
    return err.message;
  }
};

const createMedicationRecord = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const { pen, medication_desc, drug, supplier, quantity, price } = req.body;
    const foundPen = await BirdPen.findOne({ code: pen });
    const stock_code = foundPen.stock;
    const amount = price * quantity;
    const date = new Date();
    const createdMedication = await managementService.createMedicationRecord(
      pen,
      date,
      stock_code,
      medication_desc,
      drug,
      supplier,
      quantity,
      price,
      amount
    );
    if (createdMedication.date) {
      let message = "medication record  created successfuly";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/record/medication");
    } else {
      let message = "something went wrong try again later";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/record/medication");
    }
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/record/medication");
  }
};

const getAllMedicationRecord = async (req, res) => {
  try {
    const allMedicationRecord = await MedicationRecord.find();
    return res.json(allMedicationRecord);
  } catch (err) {
    console.log(err);
    return res.json(err.message);
  }
};

const createDailyProductionRecord = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const {
      pen,
      mortality,
      feed_code,
      feed_unit,
      feed_qty,
      big_Eggs,
      b_egg_price,
      small_egg,
      s_egg_price,
      broken_eggs_crate,
      broken_egg_price,
    } = req.body;

    let date = new Date();
    // Get current bird stock to calculate production percentage
    const foundBirdPen = await BirdPen.findOne({ code: pen });
    if (!foundBirdPen) {
      let message = "no bird pen found with this name";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/record/dailyproduction");
    }
    /**
     * Calculate Production Percentage for Poultry Farm
     *
     * Production Percentage = (Total Eggs Produced / Total Birds) × 100
     *
     * - Total Eggs Produced = (big_Eggs + unsorted_Eggs + small_egg) * 30
     *   (Assuming 30 eggs per crate)
     * - Uses birds at start of day (before mortality)
     */
    function calculateProductionPercentage() {
      const initialBirds = Number(foundBirdPen.stock);
      const eggsPerCrate = 30;
      const totalEggsProduced =
        (Number(big_Eggs) + Number(broken_eggs_crate) + Number(small_egg)) *
        eggsPerCrate;

      if (initialBirds <= 0) {
        return 0;
      }

      const productionPercentage = (totalEggsProduced / initialBirds) * 100;
      // Cap at 120% for rare cases
      const finalPercentage = Math.min(productionPercentage, 100);

      // Round to 2 decimal places
      return Math.round(finalPercentage * 100) / 100;
    }
    const calculateTotalEggPrice = () => {
      let smallEggPrice = small_egg * s_egg_price;
      let bidEggPrice = big_Eggs * b_egg_price;
      let brokenEggPrice = broken_eggs_crate * broken_egg_price;

      return smallEggPrice + bidEggPrice + brokenEggPrice;
    };
    // check if no of bag taken is up to no of bag in stock
    const foundFeedDetails = await FeedDetails.findOne({ code: feed_code });
    if (!foundFeedDetails.code) {
      let message = "no feed found with this code and unit";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/record/dailyproduction");
    }

    console.log("foundFeed stock:", foundFeedDetails.stock);
    if (Number(feed_qty) > foundFeedDetails.stock) {
      console.log("get here 001");
      let message = "the feed avalable is not up to the feed inputed";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/record/dailyproduction");
    }
    //check if todays record already exist
    const foundRecord = await DailyProductionRecord.findOne({
      date: date,
      pen: pen,
    });
    if (foundRecord) {
      let message =
        "dailyproduction record already created for this pen today if to made a mistake delete the existing one";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/record/dailyproduction");
    }

    //else create record
    const createdDailyProductionRecord =
      await managementService.createDailyProductionRecord(
        pen,
        date,
        mortality,
        feed_code,
        feed_unit,
        feed_qty,
        big_Eggs,
        b_egg_price,
        small_egg,
        s_egg_price,
        broken_eggs_crate,
        broken_egg_price,
        calculateTotalEggPrice(),
        calculateProductionPercentage()
      );
    if (createdDailyProductionRecord.date) {
      let message = "daily production record  created successfuly";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/record/dailyproduction");
    } else {
      let message = "something went wrong try again later";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/record/dailyproduction");
    }
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/record/dailyproduction");
  }
};

const getAllProductionRecord = async (req, res) => {
  try {
    const AllProductionRecord = await DailyProductionRecord.find();
    return res.json(AllProductionRecord);
  } catch (err) {
    console.log(err);
    return res.json(err.message);
  }
};

const deleteDailyProduction = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const { date, pen } = req.body;
    const staff = req.user;
    const foundRecord = await DailyProductionRecord.findOne({
      date: date,
      pen: pen,
    });
    const deletedRecord = await DailyProductionRecord.deleteOne({
      date: date,
      pen: pen,
    });
    if (deletedRecord.deletedCount > 0) {
      await managementService.createDeletedDailyRecord(foundRecord, staff);
      let message = "record deleted successfuly !!";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/management/homepage");
    } else {
      let message = "no record with this date and pen found";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/management/homepage");
    }
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/management/homepage");
  }
};

const deleteMedicationRecord = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const { date, pen } = req.body;
    const deletedRecord = await MedicationRecord.deleteOne({
      date: date,
      pen: pen,
    });
    if (deletedRecord.deletedCount > 0) {
      let message = "record deleted successfuly !!";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/management/homepage");
    } else {
      let message = "no record with this date found";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/management/homepage");
    }
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/management/homepage");
  }
};

const deleteSuppliesRecord = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const { date, pen } = req.body;
    const deletedRecord = await SuppliesRecord.deleteOne({
      date: date,
      pen: pen,
    });
    if (deletedRecord.deletedCount > 0) {
      let message = "record deleted successfuly !!";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/management/homepage");
    } else {
      let message = "no record with this date found";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/management/homepage");
    }
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/management/homepage");
  }
};

const deleteSalesRecord = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const { date, pen } = req.body;
    const deletedRecord = await SalesRecord.deleteOne({
      date: date,
      pen: pen,
    });
    if (deletedRecord.deletedCount > 0) {
      let message = "record deleted successfuly !!";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/management/homepage");
    } else {
      let message = "no record with this date found";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/management/homepage");
    }
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/management/homepage");
  }
};

const getAllDeletedDailyProductionRecords = async (req, res) => {
  try {
    const allRecords = await DeletedDailyRecord.find();
    return res.json(allRecords);
  } catch (err) {
    console.log(err);
    return res.json(err.message);
  }
};

module.exports = {
  createSuppliesRecord,
  getAllSuppliesRecord,
  createSalesRecord,
  getAllSalesRecord,
  createMedicationRecord,
  getAllMedicationRecord,
  createDailyProductionRecord,
  getAllProductionRecord,
  deleteDailyProduction,
  deleteMedicationRecord,
  deleteSuppliesRecord,
  deleteSalesRecord,
  getAllDeletedDailyProductionRecords,
};
