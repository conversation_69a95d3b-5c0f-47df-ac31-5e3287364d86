const fromInput = document.getElementById("from");
const toInput = document.getElementById("to");
const submitBtn = document.getElementById("submit");
const dateFilterForm = document.getElementById("dateFilterForm");
const recordContainer = document.getElementById("record-container");

// Listen for filter form submit
if (dateFilterForm) {
  dateFilterForm.addEventListener("submit", function (e) {
    e.preventDefault();
    let payload = {
      date_from: fromInput.value,
      date_to: toInput.value,
    };
    // Fetch filtered records from the server
    fetch("/report/dailyproduction", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    })
      .then((res) => res.json())
      .then((data) => {
        // Build filtered record rows
        let html = `
          <div class='record-header' id='record-header'>
                <p>Date</p>
                <p>Pen</p>
                <p>Stock Code</p>
                <p>Mortality</p>
                <p>Feed Code</p>
                <p>Feed Unit</p>
                <p>Feed Qty</p>
                <p>Big Eggs</p>
                <p>B_Egg Price</p>
                <p>Small Eggs</p>
                <p>S_Egg Price</p>
                <p>broken Eggs</p>
                <p>Broken Egg Price</p>
                <p>Total Price</p>
                <p>Production %</p>
                <span style="width: 48px"></span>
          </div>
        `;
        if (data.length > 0) {
          data.forEach(function (record) {
            html += `
              <div class='record-row'>
                <p>${new Date(record.date).toLocaleDateString()}</p>
                <p>${record.pen}</p>
                <p>${record.stock_code}</p>                 
                <p>${record.mortality}</p>                 
                <p>${record.feed_code}</p>                 
                <p>${record.feed_unit}</p>                 
                <p>${record.feed_qty}</p>                 
                <p>${record.big_eggs}</p>                 
                <p>${record.b_egg_price}</p>                 
                <p>${record.small_eggs}</p>                 
                <p>${record.s_egg_price}</p>                 
                <p>${record.broken_eggs_crate}</p>                 
                <p>${record.broken_egg_price}</p>                 
                <p>${record.total_egg_price}</p>                 
                <p>${record.prod_percent}</p>  
                 <a href="/deleterecord/dailyproductionrecord/${
                   record._id
                 }" >               
          <i class="material-icons" style="font-size: 32px; color: red" >delete</i>                 
              </div>                 
            `;
          });
        } else {
          html += `<p style='text-align:center'>no record found</p>`;
        }
        recordContainer.innerHTML = html;
      })
      .catch((err) => {
        recordContainer.innerHTML = `<p style='text-align:center;color:red'>Error loading records</p>`;
      });
  });
}
