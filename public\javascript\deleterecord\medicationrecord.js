const fromInput = document.getElementById("from");
const toInput = document.getElementById("to");
const submitBtn = document.getElementById("submit");
const dateFilterForm = document.getElementById("dateFilterForm");
const recordContainer = document.getElementById("record-container");

// Listen for filter form submit
if (dateFilterForm) {
  dateFilterForm.addEventListener("submit", function (e) {
    e.preventDefault();
    let payload = {
      date_from: fromInput.value,
      date_to: toInput.value,
    };
    // Fetch filtered records from the server
    fetch("/report/medication", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    })
      .then((res) => res.json())
      .then((data) => {
        // Build filtered record rows
        let html = `
          <div class='record-header' id='record-header'>
                <p>Date</p>
                <p>Pen</p>
                <p>Stock Code</p>
                <p>Drug/Medicine</p>
                <p>Medication Description</p>
                <p>Supplier</p>
                <p>Quantity</p>
                <p>Price per Unit</p>
                <p>Total Amount</p>
                <span style="width: 48px"></span>
          </div>
        `;
        if (data.length > 0) {
          data.forEach(function (record) {
            html += `
              <div class='record-row'>
                <p>${new Date(record.date).toLocaleDateString()}</p>
                <p>${record.pen}</p>                 
                <p>${record.stock_code}</p>                 
                <p>${record.drug}</p>                 
                <p>${record.medication_desc}</p>                 
                <p>${record.supplier}</p>                 
                <p>${record.quantity}</p>                 
                <p>${record.price}</p>                 
                <p>${record.amount}</p>                 
                 <a href="/deleterecord/medicationrecord/${
                   record._id
                 }" >               
          <i class="material-icons" style="font-size: 32px; color: red" >delete</i>                 
              </div>                 
            `;
          });
        } else {
          html += `<p style='text-align:center'>no record found</p>`;
        }
        recordContainer.innerHTML = html;
      })
      .catch((err) => {
        recordContainer.innerHTML = `<p style='text-align:center;color:red'>Error loading records</p>`;
      });
  });
}
