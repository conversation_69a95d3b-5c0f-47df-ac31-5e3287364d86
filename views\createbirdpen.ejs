<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/global-styles.css" />
    <title>Create Bird Pen - Farm Management System</title>
  </head>
  <body>
    <!-- Modern Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="app-logo">Farm Management System</div>
        <div class="user-info">
          <span>Create Bird Pen</span>
        </div>
      </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav">
      <div class="breadcrumb-content">
        <a href="/management/homepage" class="breadcrumb-item">Dashboard</a>
        <span class="breadcrumb-separator">›</span>
        <a href="/management/homepage" class="breadcrumb-item">Management</a>
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-item active">Create Bird Pen</span>
      </div>
    </nav>

    <!-- Main Container -->
    <div class="app-container">
      <!-- Alerts -->
      <% if(hasErr){%>
      <div class="alert alert-danger">
        <div>
          <% errMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%> <% if(hasSuccess){%>
      <div class="alert alert-success">
        <div>
          <% successMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%>

      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">🏠 Create Bird Pen</h1>
        <p class="page-subtitle">
          Create or update bird pen housing facilities
        </p>
        <div class="page-actions">
          <a href="/management/homepage" class="btn btn-secondary btn-back"
            >Back to Dashboard</a
          >
          <a href="/management/check-birdpen" class="btn btn-outline"
            >View All Pens</a
          >
        </div>
      </div>

      <!-- Bird Pen Form -->
      <div class="form-container">
        <form action="/management/birdpen" method="POST" id="birdPenForm">
          <!-- Pen Identification -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">🏷️ Pen Identification</h3>
            </div>
            <div class="card-body">
              <div class="form-grid">
                <div class="form-group">
                  <label for="code" class="form-label">🏷️ Pen Code</label>
                  <input
                    type="text"
                    class="form-control"
                    name="code"
                    id="code"
                    placeholder="Enter unique pen code (e.g., PEN001)"
                    required
                    style="text-transform: uppercase"
                  />
                  <small class="text-muted"
                    >Use a unique identifier for this pen</small
                  >
                </div>

                <div class="form-group">
                  <label for="name" class="form-label">🏠 Pen Name</label>
                  <input
                    type="text"
                    class="form-control"
                    name="name"
                    id="name"
                    placeholder="Enter descriptive pen name"
                    required
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Pen Specifications -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">📊 Pen Specifications</h3>
            </div>
            <div class="card-body">
              <div class="form-grid">
                <div class="form-group">
                  <label for="cage_capacity" class="form-label"
                    >🏠No Of Cage</label
                  >
                  <input
                    type="number"
                    class="form-control"
                    name="no_of_cage"
                    id="no_of_cage"
                    placeholder="Enter maximum bird capacity"
                    required
                  />
                  <small class="text-muted"
                    >Maximum number of cages this pen can hold</small
                  >
                </div>
                <div class="form-group">
                  <label for="cage_capacity" class="form-label"
                    >🏠 Cage Capacity</label
                  >
                  <input
                    type="number"
                    class="form-control"
                    name="cage_capacity"
                    id="cage_capacity"
                    placeholder="Enter maximum bird capacity"
                    required
                  />
                  <small class="text-muted"
                    >Maximum number of birds this cage can hold</small
                  >
                </div>

                <div class="form-group">
                  <label for="stock" class="form-label"
                    >🐔 Total Current Stock</label
                  >
                  <input
                    type="number"
                    class="form-control"
                    name="stock"
                    id="stock"
                    placeholder="Enter current bird count"
                    required
                  />
                </div>
                <div class="form-group">
                  <label for="type" class="form-label">🐔 Bird Type</label>
                  <select
                    name="type"
                    class="form-control form-select"
                    id="type"
                    required
                  >
                    <option value="">Select bird type...</option>
                    <option value="Layer">Layer</option>
                    <option value="Broiler">Broiler</option>
                    <option value="Cockerel">Cockerel</option>
                    <option value="Native">Native</option>
                  </select>
                </div>

                <div class="form-group">
                  <label for="batch" class="form-label">📋 Batch Code</label>
                  <input
                    type="text"
                    class="form-control"
                    name="batch"
                    id="batch"
                    placeholder="Enter batch identifier"
                    required
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Pen Summary -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">📊 Pen Summary</h3>
            </div>
            <div class="card-body">
              <div class="grid grid-4">
                <div class="text-center">
                  <h4 class="text-primary" id="penCode">-</h4>
                  <p class="text-muted">Pen Code</p>
                </div>
                <div class="text-center">
                  <h4 class="text-info" id="maxCapacity">0</h4>
                  <p class="text-muted">Max Capacity</p>
                </div>
                <div class="text-center">
                  <h4 class="text-success" id="currentStock">0</h4>
                  <p class="text-muted">Current Stock</p>
                </div>
                <div class="text-center">
                  <h4 class="text-warning" id="occupancyRate">0%</h4>
                  <p class="text-muted">Occupancy Rate</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="d-flex justify-between align-center">
            <div>
              <span class="text-muted">All fields are required</span>
            </div>
            <div class="d-flex gap-2">
              <button type="reset" class="btn btn-secondary">
                🔄 Reset Form
              </button>
              <button type="submit" class="btn btn-success btn-save">
                🏠 Create/Update Pen
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Enhanced JavaScript -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const codeInput = document.getElementById("code");
        const capacityInput = document.getElementById("cage_capacity");
        const stockInput = document.getElementById("stock");
        const noOfCageInput = document.getElementById("no_of_cage");
        const penCodeDisplay = document.getElementById("penCode");
        const maxCapacityDisplay = document.getElementById("maxCapacity");
        const currentStockDisplay = document.getElementById("currentStock");
        const occupancyRateDisplay = document.getElementById("occupancyRate");

        // Auto-uppercase pen code
        codeInput.addEventListener("input", function () {
          this.value = this.value.toUpperCase();
        });

        function updateDisplays() {
          const code = codeInput.value || "-";
          const noOfCage = parseInt(noOfCageInput.value) || 0;
          const capacity = parseInt(capacityInput.value) || 0;
          const stock = parseInt(stockInput.value) || 0;
          const maxCapacity = noOfCage * capacity;
          const occupancy =
            maxCapacity > 0 ? ((stock / maxCapacity) * 100).toFixed(1) : 0;

          penCodeDisplay.textContent = code;
          maxCapacityDisplay.textContent = maxCapacity.toLocaleString();
          currentStockDisplay.textContent = stock.toLocaleString();
          occupancyRateDisplay.textContent = occupancy + "%";

          // Color coding for occupancy rate
          if (occupancy > 90) {
            occupancyRateDisplay.className = "text-danger";
          } else if (occupancy > 75) {
            occupancyRateDisplay.className = "text-warning";
          } else {
            occupancyRateDisplay.className = "text-success";
          }
        }

        // Add event listeners
        [codeInput, capacityInput, stockInput, noOfCageInput].forEach(
          (input) => {
            input.addEventListener("input", updateDisplays);
          }
        );

        // Validate stock doesn't exceed max capacity
        stockInput.addEventListener("input", function () {
          const noOfCage = parseInt(noOfCageInput.value) || 0;
          const capacity = parseInt(capacityInput.value) || 0;
          const maxCapacity = noOfCage * capacity;
          const stock = parseInt(this.value) || 0;
          if (stock > maxCapacity && maxCapacity > 0) {
            this.setCustomValidity("Stock cannot exceed total pen capacity");
          } else {
            this.setCustomValidity("");
          }
        });

        // Form submission enhancement
        const form = document.getElementById("birdPenForm");
        form.addEventListener("submit", function (e) {
          const submitBtn = form.querySelector('button[type="submit"]');
          submitBtn.innerHTML = '<span class="loading"></span> Creating...';
          submitBtn.disabled = true;
        });

        // Initialize displays
        updateDisplays();
      });
    </script>
  </body>
</html>
