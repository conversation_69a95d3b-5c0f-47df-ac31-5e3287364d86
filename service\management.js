const Staff = require("../model/Staff");
const Supplier = require("../model/Supplier");
const Customer = require("../model/Customer");
const BirdIntake = require("../model/BirdIntake");
const FeedDetails = require("../model/FeedDetails");
const DailyProductionRecord = require("../model/DailyProductionRecord");
const MedicationRecord = require("../model/MedicationRecord");
const SalesRecord = require("../model/SalesRecord");
const SuppliesRecord = require("../model/SuppliesRecord");
const argon = require("argon2");
const FeedType = require("../model/FeedType");
const BirdPen = require("../model/BirdPen");
const BirdRemoved = require("../model/BirdRemoved");
const DeletedDailyRecord = require("../model/DeletedDailyRecord");

const createStaff = async (
  staff_no,
  name,
  level,
  address,
  account_no,
  bank,
  guarantor,
  password,
  Expected_salary
) => {
  try {
    let hashPassword = await argon.hash(password);

    const createdStaff = new Staff({
      staff_no,
      name,
      level,
      address,
      account_no,
      bank,
      guarantor,
      password: hashPassword,
      expected_salary: Expected_salary,
    });

    const result = await createdStaff.save();
    return result;
  } catch (err) {
    return err;
  }
};

const createSupplier = async (
  supplier_id,
  name,
  address,
  account_balance,
  account_no,
  bank
) => {
  try {
    const createdSupplier = new Supplier({
      supplier_id,
      name,
      address,
      account_balance,
      account_no,
      bank,
    });
    const result = await createdSupplier.save();
    return result;
  } catch (err) {
    return err;
  }
};

const createCustomer = async (
  name,
  address,
  account_balance,
  account_no,
  bank
) => {
  try {
    const customer_id = "cus" + Math.floor(Math.random() * ********** + 1);
    const createdCustomer = new Customer({
      customer_id,
      name,
      address,
      account_balance,
      account_no,
      bank,
    });

    const result = await createdCustomer.save();
    return result;
  } catch (err) {
    return err;
  }
};

const createBirdIntake = async (
  batch_code,
  type,
  date_taken,
  no_of_bird,
  supplier,
  pen,
  price,
  cost,
  level,
  attenedant_name,
  attenedant_staff_no
) => {
  try {
    const createdBirdIntake = new BirdIntake({
      batch_code,
      type,
      date_taken,
      no_of_bird,
      supplier,
      pen,
      price,
      cost,
      level,
      attenedant_name,
      attenedant_staff_no,
    });

    //add to stock in bird pen
    const foundPen = await BirdPen.findOne({ code: pen });
    if (foundPen) {
      let newStock = Number(foundPen.stock) + Number(no_of_bird);
      foundPen.stock = newStock;
      await foundPen.save();
    }

    const result = await createdBirdIntake.save();
    return result;
  } catch (err) {
    console.log(err);
    return err;
  }
};

const createFeedDetails = async (
  code,
  stock,
  reorder_L,
  supplier,
  unit,
  price
) => {
  try {
    const createdFeedDetails = new FeedDetails({
      code,
      stock,
      reorder_L,
      supplier,
      unit,
      price,
    });
    const result = await createdFeedDetails.save();
    return result;
  } catch (err) {
    console.log(err);
    return err;
  }
};

const createDailyProductionRecord = async (
  pen,
  date,
  mortality,
  feed_code,
  feed_unit,
  feed_qty,
  big_eggs,
  b_egg_price,
  small_eggs,
  s_egg_price,
  broken_eggs_crate,
  broken_egg_price,
  total_egg_price,
  prod_percent
) => {
  try {
    const foundBirdPen = await BirdPen.findOne({ code: pen });
    console.log("got here service:", foundBirdPen.stock);
    let stock_code = Number(foundBirdPen.stock) - Number(mortality);

    const createdDailyProductionRecord = new DailyProductionRecord({
      pen,
      date,
      stock_code: stock_code,
      mortality,
      feed_code,
      feed_unit,
      feed_qty,
      big_eggs,
      b_egg_price,
      small_eggs,
      s_egg_price,
      broken_eggs_crate,
      broken_egg_price,
      total_egg_price,
      prod_percent,
    });

    const result = await createdDailyProductionRecord.save();
    // transactions after record is been confirmed
    if (mortality > 0) {
      const foundBirdPen = await BirdPen.findOne({ code: pen });
      if (foundBirdPen) {
        let newStock = Number(foundBirdPen.stock) - Number(mortality);
        foundBirdPen.stock = newStock;
        await foundBirdPen.save();
      }
    }

    if (feed_qty > 0) {
      const foundFeedDetails = await FeedDetails.findOne({
        code: feed_code,
        unit: feed_unit,
      });
      if (foundFeedDetails) {
        let newStock = Number(foundFeedDetails.stock) - Number(feed_qty);
        foundFeedDetails.stock = newStock;
        await foundFeedDetails.save();
      }
    }
    return result;
  } catch (err) {
    console.log(err);
    return err;
  }
};

const createMedicationRecord = async (
  pen,
  date,
  stock_code,
  medication_desc,
  drug,
  supplier,
  quantity,
  price,
  amount
) => {
  try {
    const createdMedication = new MedicationRecord({
      pen,
      date,
      stock_code,
      medication_desc,
      drug,
      supplier,
      quantity,
      price,
      amount,
    });
    const result = await createdMedication.save();
    return result;
  } catch (err) {
    console.log(err);
    return err;
  }
};

const createSalesRecord = async (
  date,
  customer,
  big_egg_crate,
  big_egg_price,
  small_egg_crate,
  small_egg_price,
  broken_egg_crate,
  broken_egg_price,
  total_price,
  remark,
  staff_name,
  staff_no
) => {
  try {
    const createdSales = new SalesRecord({
      date,
      customer,
      big_egg_crate: big_egg_crate ? big_egg_crate : 0,
      big_egg_price: big_egg_price ? big_egg_price : 0,
      small_egg_crate: small_egg_crate ? small_egg_crate : 0,
      small_egg_price: small_egg_price ? small_egg_price : 0,
      broken_egg_crate: broken_egg_crate ? broken_egg_crate : 0,
      broken_egg_price: broken_egg_price ? broken_egg_price : 0,
      total_price,
      remark,
      staff_name,
      staff_no,
    });
    const result = await createdSales.save();
    return result;
  } catch (err) {
    console.log(err);
    return err;
  }
};

const createSuppliesRecord = async (
  date,
  supplier,
  feed_type,
  feed_unit,
  stock_bf,
  price,
  quantity,
  amount,
  remark,
  stock_cf,
  staff_name,
  staff_no
) => {
  try {
    const createdSupplies = new SuppliesRecord({
      date,
      supplier,
      feed_type,
      feed_unit,
      stock_bf,
      price,
      quantity,
      amount,
      remark,
      stock_cf,
      staff_name,
      staff_no,
    });
    const result = await createdSupplies.save();

    // increase the stock in feed details record
    const foundFeedDetails = await FeedDetails.findOne({
      code: feed_type,
      unit: feed_unit,
    });
    if (foundFeedDetails) {
      let newStock = Number(foundFeedDetails.stock) + Number(quantity);
      foundFeedDetails.stock = newStock;
      await foundFeedDetails.save();
    }
    return result;
  } catch (err) {
    console.log(err);
    return err;
  }
};

const createFeedType = async (code, type, Disc) => {
  try {
    const createdFeedType = new FeedType({
      code,
      type,
      Disc,
    });
    const result = await createdFeedType.save();
    return result;
  } catch (err) {
    console.log(err);
    return err;
  }
};

const updateCustomer = async (
  oldname,
  name,
  address,
  account_balance,
  account_no,
  bank
) => {
  try {
    // Case-insensitive search for the customer
    const foundCustomer = await Customer.findOne({
      name: { $regex: new RegExp(oldname, "i") },
    });
    if (foundCustomer) {
      foundCustomer.name = name;
      foundCustomer.address = address;
      foundCustomer.account_balance = account_balance;
      foundCustomer.account_no = account_no;
      foundCustomer.bank = bank;
      const result = await foundCustomer.save();
      return result;
    } else {
      return {
        status: "error",
        msg: "no customer with this name",
      };
    }
  } catch (err) {
    console.log(err);
    return err;
  }
};

const updateSupplier = async (
  supplier_id,
  name,
  address,
  account_balance,
  account_no,
  bank
) => {
  try {
    // Case-insensitive search for the supplier by name (since we're using name for identification)
    const foundSupplier = await Supplier.findOne({
      name: { $regex: new RegExp(name, "i") },
    });
    if (foundSupplier) {
      foundSupplier.name = name;
      foundSupplier.address = address;
      foundSupplier.account_balance = account_balance;
      foundSupplier.account_no = account_no;
      foundSupplier.bank = bank;
      const result = await foundSupplier.save();
      return result;
    } else {
      return {
        status: "error",
        msg: "no supplier with this name",
      };
    }
  } catch (err) {
    console.log(err);
    return err;
  }
};

const updateStaff = async (
  staff_no,
  name,
  level,
  address,
  account_no,
  bank,
  guarantor,
  Expected_salary
) => {
  try {
    // Case-insensitive search for the staff by name (since we're using name for identification)
    const foundStaff = await Staff.findOne({
      name: { $regex: new RegExp(name, "i") },
    });
    if (foundStaff) {
      foundStaff.name = name;
      foundStaff.level = level;
      foundStaff.address = address;
      foundStaff.account_no = account_no;
      foundStaff.bank = bank;
      foundStaff.guarantor = guarantor;
      foundStaff.expected_salary = Expected_salary;
      const result = await foundStaff.save();
      return result;
    } else {
      return {
        status: "error",
        msg: "no staff with this name",
      };
    }
  } catch (err) {
    console.log(err);
    return err;
  }
};

const createBirdPen = async (
  code,
  name,
  date,
  stock,
  no_of_cage,
  cage_capacity,
  batch,
  type
) => {
  try {
    const createdBirdPen = new BirdPen({
      code,
      name,
      date,
      stock,
      no_of_cage,
      cage_capacity,
      batch,
      type,
    });
    const result = await createdBirdPen.save();
    return result;
  } catch (err) {
    console.log(err);
    return err.message;
  }
};

const updateBirdPen = async (code, cage_capacity, stock, batch, type) => {
  try {
    const foundBirdPen = await BirdPen.findOne({ code });
    if (!foundBirdPen) {
      return "no birdpen found with this code";
    }
    foundBirdPen.cage_capacity = cage_capacity;
    foundBirdPen.stock = stock;
    foundBirdPen.batch = batch;
    foundBirdPen.type = type;
    const result = await foundBirdPen.save();
    return result;
  } catch (err) {
    console.log(err);
    return err.message;
  }
};

const createBirdRemoved = async (
  batch_code,
  type,
  date_taken,
  no_of_bird,
  pen,
  level,
  attenedant_name,
  attenedant_staff_no
) => {
  try {
    const createdBirdRemoved = new BirdRemoved({
      batch_code,
      type,
      date_taken,
      no_of_bird,
      pen,
      level,
      attenedant_name,
      attenedant_staff_no,
    });

    //remove from stock in bird pen
    const foundPen = await BirdPen.findOne({ code: pen });
    if (foundPen) {
      let newStock = Number(foundPen.stock) - Number(no_of_bird);
      foundPen.stock = newStock;
      await foundPen.save();
    }

    const result = await createdBirdRemoved.save();
    return result;
  } catch (err) {
    console.log(err);
    return err.message;
  }
};

const updateFeedDetails = async (
  code,
  unit,
  stock,
  reorder_l,
  supplier,
  price
) => {
  try {
    const foundFeedDetails = await FeedDetails.findOne({ code, unit });
    if (!foundFeedDetails) {
      return "no feed details found with this code and unit";
    }
    foundFeedDetails.stock = stock;
    foundFeedDetails.reorder_L = reorder_l;
    foundFeedDetails.supplier = supplier;
    foundFeedDetails.price = price;
    const result = await foundFeedDetails.save();
    return result;
  } catch (err) {
    console.log(err);
    return err.message;
  }
};

const createDeletedDailyRecord = async (dailyproduction, staff) => {
  try {
    const createdDeletedDailyRecord = new DeletedDailyRecord({
      pen: dailyproduction.pen,
      date: dailyproduction.date,
      stock_code: dailyproduction.stock_code,
      mortality: dailyproduction.mortality,
      feed_code: dailyproduction.feed_code,
      feed_unit: dailyproduction.feed_unit,
      feed_qty: dailyproduction.feed_qty,
      price: dailyproduction.price,
      totalAmt: dailyproduction.totalAmt,
      big_Eggs: dailyproduction.big_Eggs,
      unsorted_Eggs: dailyproduction.unsorted_Eggs,
      small: dailyproduction.small,
      total_crates: dailyproduction.total_crates,
      egg_Pieces: dailyproduction.egg_Pieces,
      prod_percent: dailyproduction.prod_percent,
      staff_that_delete: staff.name,
      staff_that_delete_no: staff.staff_no,
    });
    const result = await createdDeletedDailyRecord.save();
    return result;
  } catch (err) {
    console.log(err);
    return err.message;
  }
};

module.exports = {
  createStaff,
  createSupplier,
  createCustomer,
  createBirdIntake,
  createFeedDetails,
  createDailyProductionRecord,
  createMedicationRecord,
  createSalesRecord,
  createSuppliesRecord,
  createFeedType,
  updateCustomer,
  updateSupplier,
  updateStaff,
  createBirdPen,
  updateBirdPen,
  createBirdRemoved,
  updateFeedDetails,
  createDeletedDailyRecord,
};
