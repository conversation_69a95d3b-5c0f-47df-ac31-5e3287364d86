const express = require("express");
const reportController = require("../controller/report");
const SuppliesRecord = require("../model/SuppliesRecord");
const SalesRecord = require("../model/SalesRecord");
const DailyProductionRecord = require("../model/DailyProductionRecord");
const MedicationRecord = require("../model/MedicationRecord");

const router = express.Router();

router.get("/dailyproduction", async (req, res) => {
  const allDailyProduction = await DailyProductionRecord.find();
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  let dateSample = new Date();
  let todaysDate = `${dateSample.toLocaleDateString()}  ${dateSample.toLocaleTimeString()}`;
  return res.render("dailyproductionreport", {
    dailyproduction: allDailyProduction,
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
    DATE: todaysDate,
  });
});

router.get("/supplies", async (req, res) => {
  const allSuppliesReport = await SuppliesRecord.find();
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");

  return res.render("suppliesreport", {
    Supplies: allSuppliesReport,
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
  });
});

router.get("/sales", async (req, res) => {
  const allSalesRecord = await SalesRecord.find();
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");

  return res.render("salesreport", {
    Sales: allSalesRecord,
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
  });
});

router.get("/medication", async (req, res) => {
  const allMedicationRecord = await MedicationRecord.find();
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");

  return res.render("medicationreport", {
    Medication: allMedicationRecord,
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
  });
});
router.post("/dailyproduction", reportController.getdailyProductionRecord);
router.post("/medication", reportController.getMedicationRecord);
router.post("/sales", reportController.getSalesRecord);
router.post("/supplies", reportController.getSuppliesRecord);

module.exports = router;
