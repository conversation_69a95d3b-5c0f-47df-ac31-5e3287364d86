<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/global-styles.css" />
    <script src="/javascript/recorddetails.js" defer></script>
    <title>Delete Records - Farm Management System</title>
  </head>
  <body>
    <!-- Modern Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="app-logo">Farm Management System</div>
        <div class="user-info">
          <span> Delete Record Dashboard</span>
        </div>
      </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav">
      <div class="breadcrumb-content">
        <a href="/management/homepage" class="breadcrumb-item">Dashboard</a>
        <span class="breadcrumb-separator">›</span>
        <a href="/management/homepage" class="breadcrumb-item">Management</a>
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-item active">Record Details</span>
      </div>
    </nav>

    <!-- Main Container -->
    <div class="app-container">
      <!-- Alerts -->
      <% if(hasErr){%>
      <div class="alert alert-danger">
        <div>
          <% errMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%> <% if(hasSuccess){%>
      <div class="alert alert-success">
        <div>
          <% successMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%>

      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">📊 Delete Records Dashboard</h1>
        <p class="page-subtitle">delete record from record list ...</p>
        <div class="page-actions">
          <a href="/management/homepage" class="btn btn-secondary btn-back"
            >Back to Dashboard</a
          >
        </div>
      </div>

      <!-- Record Categories -->
      <div class="record-categories">
        <!-- Farm Operations Records -->
        <div class="card mb-4">
          <div class="card-header">
            <h3 class="card-title">🐔 Farm Operations Records</h3>
            <p class="card-subtitle">Core farm management and livestock data</p>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6 mb-3">
                <a href="/deleterecord/feedtype" class="btn btn-outline w-100">
                  🌾 Delete Feed Types Record
                </a>
              </div>
              <div class="col-md-6 mb-3">
                <a
                  href="/deleterecord/feeddetail"
                  class="btn btn-outline w-100"
                >
                  📦 Delete Feed Details Record
                </a>
              </div>
              <div class="col-md-6 mb-3">
                <a
                  href="/deleterecord/birdintake"
                  class="btn btn-outline w-100"
                >
                  🐔 Delete Bird Intake Record
                </a>
              </div>
              <div class="col-md-6 mb-3">
                <a
                  href="/deleterecord/birdremove"
                  class="btn btn-outline w-100"
                >
                  🐔 Delete Bird Removed Record
                </a>
              </div>
              <div class="col-md-6 mb-3">
                <a href="/deleterecord/birdpen" class="btn btn-outline w-100">
                  🏠 Delete Bird Pens Records
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Account Management Records -->
        <div class="card mb-4">
          <div class="card-header">
            <h3 class="card-title">👥 Account Management Records</h3>
            <p class="card-subtitle">
              Customer, supplier, and staff information
            </p>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6 mb-3">
                <a href="/deleterecord/customer" class="btn btn-outline w-100">
                  👥 Delete Customers Record
                </a>
              </div>
              <div class="col-md-6 mb-3">
                <a href="/deleterecord/supplier" class="btn btn-outline w-100">
                  🏭 Delete Suppliers Record
                </a>
              </div>
              <div class="col-md-6 mb-3">
                <a href="/deleterecord/staff" class="btn btn-outline w-100">
                  👨‍💼 Delete Staff Record
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Daily Operations Records -->
        <div class="card mb-4">
          <div class="card-header">
            <h3 class="card-title">📅 Daily Operations Records</h3>
            <p class="card-subtitle">Production, sales, and operational data</p>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6 mb-3">
                <a
                  href="/deleterecord/dailyproductionrecord"
                  class="btn btn-outline w-100"
                >
                  📊 Delete Daily Production Record
                </a>
              </div>
              <div class="col-md-6 mb-3">
                <a
                  href="/deleterecord/salesrecord"
                  class="btn btn-outline w-100"
                >
                  💰 Delete Sales Records
                </a>
              </div>
              <div class="col-md-6 mb-3">
                <a
                  href="/deleterecord/medicationrecord"
                  class="btn btn-outline w-100"
                >
                  💊 Delete Medication Records
                </a>
              </div>
              <div class="col-md-6 mb-3">
                <a
                  href="/deleterecord/suppliesrecord"
                  class="btn btn-outline w-100"
                >
                  📦 Delete Supplies Records
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Results Display Section -->
      <div class="card" id="resultsCard" style="display: none">
        <div class="card-header">
          <h3 class="card-title" id="resultsTitle">📊 Record Results</h3>
          <div class="card-actions">
            <button id="exportBtn" class="btn btn-outline btn-sm">
              📄 Export Data
            </button>
            <button id="printBtn" class="btn btn-outline btn-sm">
              🖨️ Print
            </button>
            <button id="clearResultsBtn" class="btn btn-secondary btn-sm">
              ✖️ Clear
            </button>
          </div>
        </div>
        <div class="card-body">
          <div
            id="loadingIndicator"
            class="text-center py-4"
            style="display: none"
          >
            <div class="loading-spinner"></div>
            <p class="mt-2">Loading records...</p>
          </div>
          <div id="display-result" class="results-container">
            <!-- Results will be displayed here -->
          </div>
        </div>
      </div>

      <!-- Quick Stats Summary -->
      <div class="card mt-4" id="statsCard" style="display: none">
        <div class="card-header">
          <h3 class="card-title">📈 Quick Statistics</h3>
        </div>
        <div class="card-body">
          <div class="grid grid-4">
            <div class="text-center">
              <h4 class="text-primary" id="totalRecords">0</h4>
              <p class="text-muted">Total Records</p>
            </div>
            <div class="text-center">
              <h4 class="text-success" id="lastUpdated">-</h4>
              <p class="text-muted">Last Updated</p>
            </div>
            <div class="text-center">
              <h4 class="text-info" id="recordType">-</h4>
              <p class="text-muted">Record Type</p>
            </div>
            <div class="text-center">
              <h4 class="text-warning" id="dataStatus">-</h4>
              <p class="text-muted">Data Status</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced CSS -->
    <style>
      .record-categories {
        margin-bottom: 2rem;
      }

      .record-btn {
        transition: all 0.3s ease;
        padding: 1rem;
        font-weight: 500;
        border-radius: 8px;
      }

      .record-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      .record-btn:active {
        transform: translateY(0);
      }

      .results-container {
        max-height: 600px;
        overflow-y: auto;
        border-radius: 8px;
      }

      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .data-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 1rem;
      }

      .data-table th,
      .data-table td {
        padding: 0.75rem;
        text-align: left;
        border-bottom: 1px solid #dee2e6;
      }

      .data-table th {
        background-color: var(--background-secondary);
        font-weight: 600;
        color: var(--text-primary);
      }

      .data-table tbody tr:hover {
        background-color: var(--background-secondary);
      }

      .card-actions {
        display: flex;
        gap: 0.5rem;
      }

      @media (max-width: 768px) {
        .card-actions {
          flex-direction: column;
        }

        .record-btn {
          margin-bottom: 0.5rem;
        }
      }
    </style>

    <!-- Enhanced JavaScript -->
  </body>
</html>
