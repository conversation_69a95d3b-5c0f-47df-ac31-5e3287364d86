/* General Styles */
body {
  font-family: "Arial", sans-serif;
  margin: 0;
  padding: 0;
  background-color: #f8f9fa;
  color: #333;
}

/* Header */
header {
  background-color: #006400;
  color: #fff;
  padding: 20px 0;
  text-align: center;
  font-size: 1.8em;
  font-weight: bold;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Main Content */
main {
  max-width: 500px;
  margin: 50px auto;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Alert Messages */
.alert {
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 5px;
}

.alert-danger {
  background-color: #f8d7da;
  color: #842029;
  border: 1px solid #f5c2c7;
}

.alert-success {
  background-color: #d1e7dd;
  color: #0f5132;
  border: 1px solid #badbcc;
}

.error {
  margin: 0;
  font-size: 0.9em;
}

.success {
  margin: 0;
  font-size: 0.9em;
}

/* Form Styles */
form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-control {
  padding: 10px;
  font-size: 1rem;
  border-radius: 5px;
  border: 1px solid #ced4da;
  transition: border-color 0.2s ease-in-out;
}

.form-control:focus {
  outline: none;
  border-color: #006400;
  box-shadow: 0 0 5px rgba(0, 100, 0, 0.3);
}

input[type="submit"] {
  background-color: #006400;
  color: #fff;
  border: none;
  padding: 10px 15px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  border-radius: 5px;
  transition: background-color 0.3s ease-in-out;
}

input[type="submit"]:hover {
  background-color: #005200;
}
