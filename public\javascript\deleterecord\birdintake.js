const fromInput = document.getElementById("from");
const toInput = document.getElementById("to");
const submitBtn = document.getElementById("submit");
const dateFilterForm = document.getElementById("dateFilterForm");
const recordContainer = document.getElementById("record-container");

// Listen for filter form submit
if (dateFilterForm) {
  dateFilterForm.addEventListener("submit", function (e) {
    e.preventDefault();
    const from = fromInput.value;
    const to = toInput.value;
    // Fetch filtered records from the server
    fetch(`/management/filter-birdintake/${from}/${to}`)
      .then((res) => res.json())
      .then((data) => {
        // Build filtered record rows
        let html = `
          <div class='record-header' id='record-header'>
            <p>Batch code</p>
            <p>Type</p>
            <p>Date taken</p>
            <p>No Of Bird</p>
            <p>Pen</p>
            <p>Supplier</p>
            <p>Price</p>
            <p>Total amount</p>
            <p>Level</p>
            <p>Staff name</p>
            <span style='width:48px'></span>
          </div>
        `;
        if (data.length > 0) {
          data.forEach(function (record) {
            html += `
              <div class='record-row'>
                <p>${record.batch_code}</p>
                <p>${record.type}</p>
                <p>${new Date(record.date_taken).toLocaleDateString()}</p>
                <p>${record.no_of_bird}</p>
                <p>${record.pen}</p>
                <p>${record.supplier}</p>
                <p>${record.price}</p>
                <p>${record.cost}</p>
                <p>${record.level}</p>
                <p>${record.attendant_name}</p>
                <a href="/deleterecord/birdintake/${
                  record._id
                }"><i class='material-icons' style='font-size:32px;color:red'>delete</i></a>
              </div>
            `;
          });
        } else {
          html += `<p style='text-align:center'>no record found</p>`;
        }
        recordContainer.innerHTML = html;
      })
      .catch((err) => {
        recordContainer.innerHTML = `<p style='text-align:center;color:red'>Error loading records</p>`;
      });
  });
}
