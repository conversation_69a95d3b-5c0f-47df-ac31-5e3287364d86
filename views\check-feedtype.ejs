<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/global-styles.css" />
    <script src="/javascript/check-feedtype.js" defer></script>
    <title>Feed Types - Farm Management System</title>
  </head>
  <body>
    <!-- Modern Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="app-logo">Farm Management System</div>
        <div class="user-info">
          <span>Feed Type Records</span>
        </div>
      </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav no-print">
      <div class="breadcrumb-content">
        <a href="/management/homepage" class="breadcrumb-item">Dashboard</a>
        <span class="breadcrumb-separator">›</span>
        <a href="/management/homepage" class="breadcrumb-item">Management</a>
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-item active">Feed Types</span>
      </div>
    </nav>

    <!-- Main Container -->
    <div class="app-container">
      <!-- Alerts -->
      <% if(hasErr){%>
      <div class="alert alert-danger">
        <div>
          <% errMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%> <% if(hasSuccess){%>
      <div class="alert alert-success">
        <div>
          <% successMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%>

      <!-- Page Header -->
      <div class="page-header no-print">
        <h1 class="page-title">🌾 Feed Type Records</h1>
        <p class="page-subtitle">
          Manage all feed type definitions and categories
        </p>
        <div class="page-actions">
          <a href="/management/homepage" class="btn btn-secondary btn-back"
            >Back to Dashboard</a
          >
          <a href="/management/feedtype" class="btn btn-success btn-add"
            >Add New Feed Type</a
          >
          <button id="refreshBtn" class="btn btn-primary btn-refresh">
            Refresh Data
          </button>
        </div>
      </div>

      <!-- Data Table Container -->
      <div class="table-container">
        <div class="table-header no-print">
          <h3 class="table-title">Feed Type Definitions</h3>
          <div class="table-actions">
            <div id="loading-indicator" class="d-none">
              <span class="loading"></span> Loading...
            </div>
            <span id="record-count" class="text-muted">Loading records...</span>
          </div>
        </div>

        <table class="data-table" id="main-table">
          <thead>
            <tr>
              <th>Feed Code</th>
              <th>Feed Type</th>
              <th>Description</th>
              <th>Created Date</th>
            </tr>
          </thead>
          <tbody id="data-table-body">
            <tr>
              <td colspan="4" class="text-center p-4">
                <div class="loading"></div>
                <span class="ml-2">Loading feed type records...</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </body>
</html>
