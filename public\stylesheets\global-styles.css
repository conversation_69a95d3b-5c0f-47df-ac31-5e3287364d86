/* ===== GLOBAL DESIGN SYSTEM ===== */

/* CSS Variables for consistent theming */
:root {
  /* Primary Colors */
  --primary-color: #2c3e50;
  --primary-light: #34495e;
  --primary-dark: #1a252f;

  /* Secondary Colors */
  --secondary-color: #3498db;
  --secondary-light: #5dade2;
  --secondary-dark: #2980b9;

  /* Accent Colors */
  --accent-color: #e74c3c;
  --accent-light: #ec7063;
  --accent-dark: #c0392b;

  /* Success/Warning/Info Colors */
  --success-color: #27ae60;
  --success-light: #58d68d;
  --warning-color: #f39c12;
  --warning-light: #f8c471;
  --info-color: #17a2b8;
  --info-light: #5dade2;

  /* Neutral Colors */
  --white: #ffffff;
  --light-gray: #f8f9fa;
  --gray: #6c757d;
  --dark-gray: #495057;
  --black: #212529;

  /* Shadows */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-xxl: 3rem;

  /* Typography */
  --font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-xxl: 1.5rem;
  --font-size-xxxl: 2rem;
}

/* ===== GLOBAL RESET ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--black);
  background-color: var(--light-gray);
}

/* ===== LAYOUT COMPONENTS ===== */

/* Modern Header */
.app-header {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  color: var(--white);
  padding: var(--spacing-lg) 0;
  box-shadow: var(--shadow-md);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.app-logo {
  font-size: var(--font-size-xxl);
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.app-logo::before {
  content: "🐔";
  font-size: var(--font-size-xxxl);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--secondary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: var(--font-size-lg);
}

/* Navigation Breadcrumb */
.breadcrumb-nav {
  background: var(--white);
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid #e9ecef;
}

.breadcrumb-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.breadcrumb-item {
  color: var(--gray);
  text-decoration: none;
  font-size: var(--font-size-sm);
  transition: color 0.3s ease;
}

.breadcrumb-item:hover {
  color: var(--secondary-color);
}

.breadcrumb-item.active {
  color: var(--primary-color);
  font-weight: 600;
}

.breadcrumb-separator {
  color: var(--gray);
  margin: 0 var(--spacing-xs);
}

/* Main Container */
.app-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-xl) var(--spacing-lg);
  min-height: calc(100vh - 200px);
}

/* Page Header */
.page-header {
  background: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  box-shadow: var(--shadow-sm);
  border-left: 4px solid var(--secondary-color);
}

.page-title {
  font-size: var(--font-size-xxxl);
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
}

.page-subtitle {
  color: var(--gray);
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-lg);
}

.page-actions {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

/* ===== BUTTON SYSTEM ===== */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-primary {
  background: var(--secondary-color);
  color: var(--white);
}

.btn-primary:hover {
  background: var(--secondary-dark);
  color: var(--white);
}

.btn-secondary {
  background: var(--gray);
  color: var(--white);
}

.btn-secondary:hover {
  background: var(--dark-gray);
  color: var(--white);
}

.btn-success {
  background: var(--success-color);
  color: var(--white);
}

.btn-success:hover {
  background: #229954;
  color: var(--white);
}

.btn-warning {
  background: var(--warning-color);
  color: var(--white);
}

.btn-warning:hover {
  background: #d68910;
  color: var(--white);
}

.btn-danger {
  background: var(--accent-color);
  color: var(--white);
}

.btn-danger:hover {
  background: var(--accent-dark);
  color: var(--white);
}

.btn-outline {
  background: transparent;
  border: 2px solid var(--secondary-color);
  color: var(--secondary-color);
}

.btn-outline:hover {
  background: var(--secondary-color);
  color: var(--white);
}

.btn-sm {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
}

.btn-lg {
  padding: var(--spacing-lg) var(--spacing-xl);
  font-size: var(--font-size-lg);
}

/* Button Icons */
.btn-icon::before {
  font-size: var(--font-size-lg);
}

.btn-back::before {
  content: "←";
}
.btn-home::before {
  content: "🏠";
}
.btn-refresh::before {
  content: "🔄";
}
.btn-print::before {
  content: "🖨️";
}
.btn-add::before {
  content: "+";
}
.btn-edit::before {
  content: "✏️";
}
.btn-delete::before {
  content: "🗑️";
}
.btn-save::before {
  content: "💾";
}
.btn-search::before {
  content: "🔍";
}
.btn-download::before {
  content: "⬇️";
}

/* ===== CARD SYSTEM ===== */
.card {
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--light-gray), var(--white));
  border-bottom: 1px solid #e9ecef;
}

.card-title {
  font-size: var(--font-size-xl);
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
}

.card-body {
  padding: var(--spacing-lg);
}

.card-footer {
  padding: var(--spacing-lg);
  background: var(--light-gray);
  border-top: 1px solid #e9ecef;
}

/* ===== ALERT SYSTEM ===== */
.alert {
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-lg);
  border-left: 4px solid;
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.alert-success {
  background: #d4edda;
  color: #155724;
  border-left-color: var(--success-color);
}

.alert-danger {
  background: #f8d7da;
  color: #721c24;
  border-left-color: var(--accent-color);
}

.alert-warning {
  background: #fff3cd;
  color: #856404;
  border-left-color: var(--warning-color);
}

.alert-info {
  background: #d1ecf1;
  color: #0c5460;
  border-left-color: var(--info-color);
}

.alert::before {
  font-size: var(--font-size-xl);
}

.alert-success::before {
  content: "✅";
}
.alert-danger::before {
  content: "❌";
}
.alert-warning::before {
  content: "⚠️";
}
.alert-info::before {
  content: "ℹ️";
}

/* ===== TABLE SYSTEM ===== */
.table-container {
  background: var(--white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-xl);
}

.table-header {
  padding: var(--spacing-lg);
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  color: var(--white);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  font-size: var(--font-size-xl);
  font-weight: bold;
}

.table-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--font-size-sm);
}

.data-table th {
  background: var(--light-gray);
  padding: var(--spacing-md);
  text-align: left;
  font-weight: 600;
  color: var(--primary-color);
  border-bottom: 2px solid var(--secondary-color);
}

.data-table td {
  padding: var(--spacing-md);
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.3s ease;
}

.data-table tbody tr:hover {
  background-color: var(--light-gray);
}

.data-table tbody tr:nth-child(even) {
  background-color: #f8f9fa;
}

/* Status indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 600;
}

.status-success {
  background: var(--success-light);
  color: var(--success-color);
}

.status-warning {
  background: var(--warning-light);
  color: var(--warning-color);
}

.status-danger {
  background: var(--accent-light);
  color: var(--accent-color);
}

.status-info {
  background: var(--info-light);
  color: var(--info-color);
}

/* ===== FORM SYSTEM ===== */
.form-container {
  background: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-xl);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
  color: var(--primary-color);
}

.form-control {
  width: 100%;
  padding: var(--spacing-md);
  border: 2px solid #e9ecef;
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  transition: all 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-control:invalid {
  border-color: var(--accent-color);
}

.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* ===== GRID SYSTEM ===== */
.grid {
  display: grid;
  gap: var(--spacing-lg);
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}
.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}
.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

@media (max-width: 768px) {
  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }
}

/* ===== UTILITY CLASSES ===== */
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}

.text-primary {
  color: var(--primary-color);
}
.text-secondary {
  color: var(--secondary-color);
}
.text-success {
  color: var(--success-color);
}
.text-warning {
  color: var(--warning-color);
}
.text-danger {
  color: var(--accent-color);
}
.text-muted {
  color: var(--gray);
}

.bg-primary {
  background-color: var(--primary-color);
}
.bg-secondary {
  background-color: var(--secondary-color);
}
.bg-success {
  background-color: var(--success-color);
}
.bg-warning {
  background-color: var(--warning-color);
}
.bg-danger {
  background-color: var(--accent-color);
}
.bg-light {
  background-color: var(--light-gray);
}

.mb-0 {
  margin-bottom: 0;
}
.mb-1 {
  margin-bottom: var(--spacing-sm);
}
.mb-2 {
  margin-bottom: var(--spacing-md);
}
.mb-3 {
  margin-bottom: var(--spacing-lg);
}
.mb-4 {
  margin-bottom: var(--spacing-xl);
}

.mt-0 {
  margin-top: 0;
}
.mt-1 {
  margin-top: var(--spacing-sm);
}
.mt-2 {
  margin-top: var(--spacing-md);
}
.mt-3 {
  margin-top: var(--spacing-lg);
}
.mt-4 {
  margin-top: var(--spacing-xl);
}

.p-0 {
  padding: 0;
}
.p-1 {
  padding: var(--spacing-sm);
}
.p-2 {
  padding: var(--spacing-md);
}
.p-3 {
  padding: var(--spacing-lg);
}
.p-4 {
  padding: var(--spacing-xl);
}

.d-flex {
  display: flex;
}
.d-grid {
  display: grid;
}
.d-block {
  display: block;
}
.d-none {
  display: none;
}

.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.justify-end {
  justify-content: flex-end;
}

.align-center {
  align-items: center;
}
.align-start {
  align-items: flex-start;
}
.align-end {
  align-items: flex-end;
}

.gap-1 {
  gap: var(--spacing-sm);
}
.gap-2 {
  gap: var(--spacing-md);
}
.gap-3 {
  gap: var(--spacing-lg);
}
.gap-4 {
  gap: var(--spacing-xl);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .app-container {
    padding: var(--spacing-md);
  }

  .header-content {
    padding: 0 var(--spacing-md);
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .page-header {
    padding: var(--spacing-lg);
  }

  .page-title {
    font-size: var(--font-size-xxl);
  }

  .page-actions {
    justify-content: center;
  }

  .btn {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
  }
}

/* ===== LOADING STATES ===== */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .no-print {
    display: none !important;
  }

  .app-header,
  .breadcrumb-nav,
  .page-actions,
  .table-actions {
    display: none !important;
  }

  .app-container {
    max-width: none;
    margin: 0;
    padding: 0;
  }

  .card,
  .table-container {
    box-shadow: none;
    border: 1px solid #ddd;
    width: 100%;
    font-size: 10pt;
  }

  .table-container {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch;
    width: 100% !important;
    padding: 0 !important;
  }
  .data-table {
    font-size: 9pt !important;
    table-layout: auto !important;
    width: 100% !important;
    border-collapse: collapse !important;
  }
  .data-table th,
  .data-table td {
    word-break: break-word !important;
    padding: 4px 6px !important;
    border: 1px solid #ccc !important;
  }
}
