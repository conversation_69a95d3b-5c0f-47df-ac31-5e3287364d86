const mongoose = require('mongoose');

const deletedDailyRecordSchema = new mongoose.Schema({
  pen: {
    type: String,
    required: true
  },
  date: {
    type: Date,
    required: true
  },
  stock_code: {
    type: Number,
    required: true
  },
  mortality: {
    type: Number,
    required: true
  },
  feed_code: {
    type: String,
    required: true
  },
  feed_unit: {
    type: String,
    required: true
  },
  feed_qty: {
    type: Number,
    required: true
  },
  price: {
    type: Number,
    required: true
  },
  totalAmt: {
    type: Number,
    required: true
  },
  big_Eggs: {
    type: Number,
    required: true
  },
  unsorted_Eggs: {
    type: Number,
    required: true
  },
  small: {
    type: Number,
    required: true
  },
  total_crates: {
    type: Number,
    required: true
  },
  egg_Pieces: {
    type: Number,
    required: true
  },
  prod_percent: {
    type: Number,
    required: true
  },
  staff_that_delete: {
    type: String,
    required: true
  },
  staff_that_delete_no: {
    type: String,
    required: true
  }
}, {
  timestamps: true
});

module.exports = mongoose.model('DeletedDailyRecord', deletedDailyRecordSchema);
