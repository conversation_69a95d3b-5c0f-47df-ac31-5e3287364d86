const express = require("express");
const managementControll = require("../controller/managemant");
const passport = require("passport");
const { getAllBirdPen, getAllFeedCode } = require("../config/tools");
const birdTypes = require("../model/TypeOfBird");
const router = express.Router();
const FeedDetails = require("../model/FeedDetails");

//protected route section
const isLoggedIn = (req, res, next) => {
  if (req.isAuthenticated()) {
    return next();
  }
  res.redirect("/");
};

router.post("/createstaff", managementControll.createStaff);
router.get("/createstaff", (req, res) => {
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("createstaff", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
  });
});
router.get("/createcustomer", (req, res) => {
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("createcustomer", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
  });
});

router.get("/createsupplier", (req, res) => {
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("createsupplier", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
  });
});
router.get("/birdintake", async (req, res) => {
  const pens = await getAllBirdPen();
  const birdType = birdTypes;
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("createbirdintake", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
    pens,
    birdType,
  });
});
router.post("/birdintake", managementControll.createBirdIntake);
router.get(
  "/findstaffbystaffno/:staffno",
  managementControll.findStaffByStaffNo
);

router.get("/homepage", isLoggedIn, async (req, res) => {
  const pens = await getAllBirdPen();
  const user = req.user;
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  let directorStatus = false;
  if (user.level == "director" || user.level == "manager") {
    directorStatus = true;
  }

  const feed_details = await FeedDetails.find();
  let reorderStatus = false;
  let reorderMsg = [];
  feed_details.forEach((list) => {
    console.log("got outside the condition:", list.reorder_L, list.stock);
    if (Number(list.reorder_L) >= Number(list.stock)) {
      reorderStatus = true;
      reorderMsg.push(`feed ${list.code} is already in reorder level !!!`);
    }
  });
  return res.render("homepage", {
    directorStatus,
    profile: user,
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
    pens,
    reorderStatus,
    reorderMsg,
  });
});
router.get("/allbirdintake", managementControll.getAllBirdInTake);
router.get("/feedtype", (req, res) => {
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("feedTypes", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
  });
});
router.get("/feeddetails", async (req, res) => {
  let feedcode = await getAllFeedCode();
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("feedDetails", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
    feedcode,
  });
});
router.get("/updatecustomer", (req, res) => {
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("updatecustomer", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
  });
});
router.get("/updatesupplier", (req, res) => {
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("updatesupplier", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
  });
});
router.get("/updatestaff", (req, res) => {
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("updatestaff", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
  });
});
router.get("/birdpen", (req, res) => {
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("createbirdpen", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
  });
});
router.get("/birdremoved", async (req, res) => {
  const pens = await getAllBirdPen();
  const birdType = birdTypes;
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("createbirdremoved", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
    pens,
    birdType,
  });
});
router.post("/birdremoved", managementControll.createBirdRemoved);
router.get("/allbirdremoved", managementControll.getAllBirdRemoved);
router.get("/allbirdpen", managementControll.getAllBirdPen);
router.post("/birdpen", managementControll.createBirdPen);
router.post("/updatestaff", managementControll.updateStaff);
router.post("/updatesupplier", managementControll.updateSupplier);
router.post("/updatecustomer", managementControll.updateCustomer);
router.get("/allfeeddetails", managementControll.getAllFeedDetails);
router.post("/feeddetails", managementControll.createFeedDetails);
router.post("/feedtype", managementControll.createFeedTypes);
router.get("/allfeedtype", managementControll.getAllFeedType);
router.get("/getallstaff", managementControll.getAllStaff);

router.post("/createsupplier", managementControll.createSupplier);

router.get("/getallsupplier", managementControll.getAllSupplier);

router.post("/createcustomer", managementControll.createCustomer);

router.get("/getallcustomer", managementControll.getAllCustomer);

// Search customer by name (case-insensitive)
router.get("/searchcustomer/:name", managementControll.searchCustomerByName);

// Search staff by name (case-insensitive)
router.get("/searchstaff/:name", managementControll.searchStaffByName);

// Search supplier by name (case-insensitive)
router.get("/searchsupplier/:name", managementControll.searchSupplierByName);

// View/Check routes for management items
router.get("/check-birdintake", isLoggedIn, (req, res) => {
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  let dateSample = new Date();
  let todaysDate = `${dateSample.toLocaleDateString()}  ${dateSample.toLocaleTimeString()}`;
  return res.render("check-birdintake", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
    DATE: todaysDate,
  });
});

router.get("/check-birdremoved", isLoggedIn, (req, res) => {
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  let dateSample = new Date();
  let todaysDate = `${dateSample.toLocaleDateString()}  ${dateSample.toLocaleTimeString()}`;
  return res.render("check-birdremoved", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
    DATE: todaysDate,
  });
});

router.get("/check-feedtype", isLoggedIn, (req, res) => {
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("check-feedtype", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
  });
});

router.get("/check-feeddetails", isLoggedIn, (req, res) => {
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("check-feeddetails", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
  });
});

router.get("/check-birdpen", isLoggedIn, (req, res) => {
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("check-birdpen", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
  });
});

// Director-only check routes
router.get("/check-customers", isLoggedIn, (req, res) => {
  const user = req.user;
  if (user.level !== "director") {
    req.flash("error", ["Access denied. Director level required."]);
    return res.redirect("/management/homepage");
  }
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("check-customers", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
  });
});

router.get("/check-suppliers", isLoggedIn, (req, res) => {
  const user = req.user;
  if (user.level !== "director") {
    req.flash("error", ["Access denied. Director level required."]);
    return res.redirect("/management/homepage");
  }
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("check-suppliers", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
  });
});

router.get("/check-staff", isLoggedIn, (req, res) => {
  const user = req.user;
  if (user.level !== "director") {
    req.flash("error", ["Access denied. Director level required."]);
    return res.redirect("/management/homepage");
  }
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("check-staff", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
  });
});

router.get("/filter-birdintake/:from/:to", managementControll.filterBirdIntake);
router.get("/filter-birdpen/:from/:to", managementControll.filterBirdPen);
router.get("/filter-birdremove/:from/:to", managementControll.filterBirdRemove);
router.get("/filter-feedtype/:from/:to", managementControll.filterFeedType);
router.get(
  "/filter-feeddetails/:from/:to",
  managementControll.filterFeedDetails
);
router.get("/filter-customer/:from/:to", managementControll.filterCustomer);
router.get("/filter-staff/:from/:to", managementControll.filterStaff);
router.get("/filter-supplier/:from/:to", managementControll.filterSupplier);

//unprotected route

router.post(
  "/login",
  passport.authenticate("staff.login", {
    failureRedirect: "/",
    failureFlash: true,
    successRedirect: "/management/homepage",
  })
);

module.exports = router;
