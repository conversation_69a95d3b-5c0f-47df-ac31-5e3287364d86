const fromInput = document.getElementById("from");
const toInput = document.getElementById("to");
const submitBtn = document.getElementById("submit");
const searchDiv = document.getElementById("searchdiv");
const printableSection = document.getElementById("printableSection");
const displayTr = document.getElementById("display-tr");
const printBtn = document.getElementById("printbtn");
const hideSec = document.getElementById("main-table");

submitBtn.addEventListener("click", (e) => {
  e.preventDefault();
  let payload = {
    date_from: fromInput.value,
    date_to: toInput.value,
  };
  fetch("/report/supplies", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(payload),
  })
    .then((res) => res.json())
    .then((data) => {
      hideSec.style.display = "none";
      let htmlContent = "";
      htmlContent += `<tr> <th>_id</th>
      <th>date</th>
      <th>supplier</th>
      <th>feed type</th>
      <th>feed unit</th>
      <th>stock bf</th>
      <th>price</th>
      <th>quantity</th>
      <th>amount</th>
      <th>remark</th>
      <th>stock cf</th>
      <th>staff name</th>
      <th>staff No</th></tr>`;
      data.forEach((item) => {
        htmlContent += `<tr>`;
        for (let key in item) {
          htmlContent += `<td> ${item[key]} </td>`;
        }
        htmlContent += `</tr>`;
      });

      displayTr.innerHTML = htmlContent;
    });
});

printBtn.addEventListener("click", (e) => {
  e.preventDefault();
  searchDiv.style.display = "none";
  printBtn.style.display = "none";
  window.print();
});
