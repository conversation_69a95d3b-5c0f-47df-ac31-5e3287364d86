// Enhanced Bird Intake Records Management
class BirdIntakeManager {
  constructor() {
    this.refreshBtn = document.getElementById("refreshBtn");
    this.dataTableBody = document.getElementById("data-table-body");
    this.printBtn = document.getElementById("printBtn");
    this.loadingIndicator = document.getElementById("loading-indicator");
    this.recordCount = document.getElementById("record-count");

    this.init();
  }

  init() {
    this.loadBirdIntakeData();
    this.setupEventListeners();
  }

  setupEventListeners() {
    // Refresh button
    this.refreshBtn.addEventListener("click", (e) => {
      e.preventDefault();
      this.loadBirdIntakeData();
    });

    // Print button
    this.printBtn.addEventListener("click", (e) => {
      e.preventDefault();
      this.printReport();
    });
  }

  showLoading() {
    this.loadingIndicator.classList.remove("d-none");
    this.refreshBtn.disabled = true;
    this.refreshBtn.innerHTML = '<span class="loading"></span> Loading...';
  }

  hideLoading() {
    this.loadingIndicator.classList.add("d-none");
    this.refreshBtn.disabled = false;
    this.refreshBtn.innerHTML = "🔄 Refresh Data";
  }

  updateRecordCount(count) {
    this.recordCount.textContent = `${count} record${
      count !== 1 ? "s" : ""
    } found`;
  }

  formatCurrency(amount) {
    return new Intl.NumberFormat("en-NG", {
      style: "currency",
      currency: "NGN",
    }).format(amount);
  }

  formatDate(dateString) {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  }

  getStatusBadge(level) {
    const statusMap = {
      director: "status-info",
      manager: "status-warning",
      supervisor: "status-success",
      staff: "status-secondary",
    };

    const statusClass = statusMap[level.toLowerCase()] || "status-secondary";
    return `<span class="status-indicator ${statusClass}">${level}</span>`;
  }

  async loadBirdIntakeData() {
    this.showLoading();

    try {
      const response = await fetch("/management/allbirdintake");
      const data = await response.json();

      let htmlContent = "";

      if (data && data.length > 0) {
        data.forEach((record, index) => {
          const dateFormatted = this.formatDate(record.date_taken);
          const priceFormatted = this.formatCurrency(record.price);
          const costFormatted = this.formatCurrency(record.cost);
          const levelBadge = this.getStatusBadge(record.level);

          htmlContent += `
            <tr style="animation: fadeIn 0.3s ease-in-out ${
              index * 0.1
            }s both;">
              <td>${dateFormatted}</td>
              <td><strong>${record.batch_code}</strong></td>
              <td>${record.type}</td>
              <td><span class="text-primary font-weight-bold">${record.no_of_bird.toLocaleString()}</span></td>
              <td>${record.supplier}</td>
              <td><span class="status-indicator status-info">${
                record.pen
              }</span></td>
              <td>${priceFormatted}</td>
              <td><strong>${costFormatted}</strong></td>
              <td>${levelBadge}</td>
              <td>${record.attenedant_name}</td>
              <td><code>${record.attenedant_staff_no}</code></td>
            </tr>
          `;
        });

        this.updateRecordCount(data.length);
      } else {
        htmlContent = `
          <tr>
            <td colspan="11" class="text-center p-4">
              <div class="text-muted">
                <h5>📭 No Records Found</h5>
                <p>No bird intake records have been created yet.</p>
                <a href="/management/birdintake" class="btn btn-primary btn-sm">
                  ➕ Create First Record
                </a>
              </div>
            </td>
          </tr>
        `;
        this.updateRecordCount(0);
      }

      this.dataTableBody.innerHTML = htmlContent;
    } catch (error) {
      console.error("Error loading bird intake data:", error);
      this.dataTableBody.innerHTML = `
        <tr>
          <td colspan="11" class="text-center p-4">
            <div class="alert alert-danger">
              <h5>❌ Error Loading Data</h5>
              <p>Failed to load bird intake records. Please try again.</p>
              <button onclick="birdIntakeManager.loadBirdIntakeData()" class="btn btn-danger btn-sm">
                🔄 Retry
              </button>
            </div>
          </td>
        </tr>
      `;
      this.updateRecordCount(0);
    } finally {
      this.hideLoading();
    }
  }

  printReport() {
    // Add print-specific styles
    const printStyles = `
      <style>
        @media print {
          .no-print { display: none !important; }
          body { font-size: 12px; }
          .data-table { font-size: 10px; }
          .data-table th, .data-table td { padding: 4px; }
        }
      </style>
    `;

    document.head.insertAdjacentHTML("beforeend", printStyles);
    window.print();
  }
}

// Add CSS animations
const animationStyles = `
  <style>
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .status-indicator {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
      border-radius: 0.25rem;
      font-weight: 600;
    }

    .status-info { background: #e3f2fd; color: #1565c0; }
    .status-success { background: #e8f5e8; color: #2e7d32; }
    .status-warning { background: #fff3e0; color: #ef6c00; }
    .status-secondary { background: #f5f5f5; color: #666; }
  </style>
`;

document.head.insertAdjacentHTML("beforeend", animationStyles);

// Initialize the manager when DOM is loaded
let birdIntakeManager;
document.addEventListener("DOMContentLoaded", () => {
  birdIntakeManager = new BirdIntakeManager();
});
