<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/global-styles.css" />
    <title>Create Bird Removal - Farm Management System</title>
  </head>
  <body>
    <!-- Modern Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="app-logo">Farm Management System</div>
        <div class="user-info">
          <span>Create Bird Removal</span>
        </div>
      </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav">
      <div class="breadcrumb-content">
        <a href="/management/homepage" class="breadcrumb-item">Dashboard</a>
        <span class="breadcrumb-separator">›</span>
        <a href="/management/homepage" class="breadcrumb-item">Management</a>
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-item active">Create Bird Removal</span>
      </div>
    </nav>

    <!-- Main Container -->
    <div class="app-container">
      <!-- Alerts -->
      <% if(hasErr){%>
      <div class="alert alert-danger">
        <div>
          <% errMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%> <% if(hasSuccess){%>
      <div class="alert alert-success">
        <div>
          <% successMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%>

      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">🚪 Create Bird Removal Record</h1>
        <p class="page-subtitle">
          Record bird removals, sales, or transfers from your farm
        </p>
        <div class="page-actions">
          <a href="/management/homepage" class="btn btn-secondary btn-back"
            >Back to Dashboard</a
          >
          <a href="/management/check-birdremoved" class="btn btn-outline"
            >View All Removals</a
          >
        </div>
      </div>
      <!-- Bird Removal Form -->
      <div class="form-container">
        <form
          action="/management/birdremoved"
          method="POST"
          id="birdRemovalForm"
        >
          <!-- Batch Information Section -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">📋 Batch Information</h3>
            </div>
            <div class="card-body">
              <div class="form-grid">
                <div class="form-group">
                  <label for="type_select" class="form-label"
                    >📋 Batch Code Prefix</label
                  >
                  <select
                    name="type_select"
                    class="form-control form-select"
                    id="type_select"
                    required
                  >
                    <option value="">Select prefix...</option>
                    <option value="L">L - Layer Birds</option>
                    <option value="B">B - Broiler Birds</option>
                    <option value="C">C - Cockerel Birds</option>
                    <option value="N">N - Native Birds</option>
                  </select>
                </div>

                <div class="form-group">
                  <label for="qty" class="form-label">🔢 Batch Quantity</label>
                  <input
                    type="number"
                    class="form-control"
                    name="qty"
                    id="qty"
                    placeholder="Enter batch quantity"
                    min="1"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="type" class="form-label">🐔 Bird Type</label>
                  <select
                    name="type"
                    class="form-control form-select"
                    id="type"
                    required
                  >
                    <option value="">Select bird type...</option>
                    <% birdType.forEach(type =>{ %>
                    <option value="<%= type %>"><%= type %></option>
                    <%}) %>
                  </select>
                </div>
              </div>
            </div>
          </div>

          <!-- Removal Details Section -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">🚪 Removal Details</h3>
            </div>
            <div class="card-body">
              <div class="form-grid">
                <div class="form-group">
                  <label for="date_taken" class="form-label"
                    >📅 Date Removed</label
                  >
                  <input
                    type="date"
                    class="form-control"
                    name="date_taken"
                    id="date_taken"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="stock" class="form-label"
                    >🔢 Number of Birds Removed</label
                  >
                  <input
                    type="number"
                    class="form-control"
                    name="stock"
                    id="stock"
                    placeholder="Enter number of birds to remove"
                    min="1"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="pen" class="form-label">🏠 Source Pen</label>
                  <select
                    name="pen"
                    class="form-control form-select"
                    id="pen"
                    required
                  >
                    <option value="">Select source pen...</option>
                    <% pens.forEach(pen =>{ %>
                    <option value="<%= pen %>"><%= pen %></option>
                    <%}) %>
                  </select>
                </div>

                <div class="form-group">
                  <label for="level" class="form-label">📊 Bird Stage</label>
                  <select
                    name="level"
                    class="form-control form-select"
                    id="level"
                    required
                  >
                    <option value="">Select bird stage...</option>
                    <option value="DOC">DOC - Day Old Chicks</option>
                    <option value="POC">POC - Point of Cage</option>
                    <option value="POL">POL - Point of Lay</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="d-flex justify-between align-center">
            <div>
              <span class="text-muted">All fields are required</span>
            </div>
            <div class="d-flex gap-2">
              <button type="reset" class="btn btn-secondary">
                🔄 Reset Form
              </button>
              <button type="submit" class="btn btn-danger btn-save">
                🚪 Record Bird Removal
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Enhanced JavaScript for form interactions -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const dateTakenInput = document.getElementById("date_taken");

        // Set today's date as default
        dateTakenInput.value = new Date().toISOString().split("T")[0];

        // Form submission enhancement
        const form = document.getElementById("birdRemovalForm");
        form.addEventListener("submit", function (e) {
          const submitBtn = form.querySelector('button[type="submit"]');
          submitBtn.innerHTML = '<span class="loading"></span> Recording...';
          submitBtn.disabled = true;
        });
      });
    </script>
  </body>
</html>
