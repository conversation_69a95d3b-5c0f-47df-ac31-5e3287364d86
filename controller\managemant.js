const managementService = require("../service/management");
const Staff = require("../model/Staff");
const Supplier = require("../model/Supplier");
const Customer = require("../model/Customer");
const BirdIntake = require("../model/BirdIntake");
const FeedType = require("../model/FeedType");
const FeedDetails = require("../model/FeedDetails");
const BirdPen = require("../model/BirdPen");
const BirdRemoved = require("../model/BirdRemoved");

const createStaff = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const {
      staff_no,
      name,
      level,
      address,
      account_no,
      bank,
      guarantor,
      password,
      Expected_salary,
    } = req.body;
    const createdStaff = await managementService.createStaff(
      staff_no,
      name,
      level,
      address,
      account_no,
      bank,
      guarantor,
      password,
      Expected_salary
    );
    if (createdStaff.name) {
      let message = "staff account created successfuly";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/management/createstaff");
    } else {
      let message = "something went wrong try again later";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/management/createstaff");
    }
  } catch (err) {
    console.log(err.message);
    return res.json(err.message);
  }
};

const findStaffByStaffNo = async (req, res) => {
  try {
    const staffNo = req.params.staffno;
    const foundStaff = await Staff.findOne({ staff_no: staffNo });
    if (!foundStaff) {
      return res.json("no staff found");
    }
    return res.json(foundStaff);
  } catch (err) {
    console.log(err.message);
    return res.json(err.message);
  }
};
const getAllStaff = async (req, res) => {
  try {
    const allStaff = await Staff.find();
    return res.json(allStaff);
  } catch (err) {
    console.log(err.message);
    return res.json(err.message);
  }
};
const createSupplier = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const { name, address, account_balance, account_no, bank, supplier_id } =
      req.body;
    const createdSupplier = await managementService.createSupplier(
      supplier_id,
      name,
      address,
      account_balance,
      account_no,
      bank
    );
    if (createdSupplier.name) {
      let message = "supplier account created successfuly";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/management/createsupplier");
    } else {
      let message = "something went wrong try again later";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/management/createsupplier");
    }
  } catch (err) {
    console.log(err.message);
    return res.redirect("/management/createsupplier");
  }
};
const getAllSupplier = async (req, res) => {
  try {
    const allSupplier = await Supplier.find();
    return res.json(allSupplier);
  } catch (err) {
    console.log(err.message);
    return res.json(err.message);
  }
};
const createCustomer = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const { name, address, account_balance, account_no, bank } = req.body;
    const createdCustomer = await managementService.createCustomer(
      name,
      address,
      account_balance,
      account_no,
      bank
    );
    if (createdCustomer.name) {
      let message = "customer account created successfuly";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/management/createcustomer");
    } else {
      let message = "something went wrong try again later";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/management/createcustomer");
    }
  } catch (err) {
    console.log(err.message);
    return res.redirect("/management/createcustomer");
  }
};
const getAllCustomer = async (req, res) => {
  try {
    const allCustomer = await Customer.find();
    return res.json(allCustomer);
  } catch (err) {
    console.log(err.message);
    return res.json(err.message);
  }
};

const searchCustomerByName = async (req, res) => {
  try {
    const { name } = req.params;
    // Case-insensitive search using regex
    const customer = await Customer.findOne({
      name: { $regex: new RegExp(name, "i") },
    });

    if (customer) {
      return res.json({
        found: true,
        customer: customer,
      });
    } else {
      return res.json({
        found: false,
        message: `No customer found with name "${name}"`,
      });
    }
  } catch (err) {
    console.log(err);
    return res.status(500).json({
      found: false,
      message: "Error searching for customer",
    });
  }
};

const searchStaffByName = async (req, res) => {
  try {
    const { name } = req.params;
    // Case-insensitive search using regex
    const staff = await Staff.findOne({
      name: { $regex: new RegExp(name, "i") },
    });

    if (staff) {
      return res.json({
        found: true,
        staff: staff,
      });
    } else {
      return res.json({
        found: false,
        message: `No staff found with name "${name}"`,
      });
    }
  } catch (err) {
    console.log(err);
    return res.status(500).json({
      found: false,
      message: "Error searching for staff",
    });
  }
};

const searchSupplierByName = async (req, res) => {
  try {
    const { name } = req.params;
    // Case-insensitive search using regex
    const supplier = await Supplier.findOne({
      name: { $regex: new RegExp(name, "i") },
    });

    if (supplier) {
      return res.json({
        found: true,
        supplier: supplier,
      });
    } else {
      return res.json({
        found: false,
        message: `No supplier found with name "${name}"`,
      });
    }
  } catch (err) {
    console.log(err);
    return res.status(500).json({
      found: false,
      message: "Error searching for supplier",
    });
  }
};
const createBirdIntake = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const {
      type_select,
      qty,
      supplier_batch,
      type,
      date_taken,
      stock,
      supplier,
      pen,
      price,
      level,
    } = req.body;
    const user = req.user;
    const batch_code =
      type_select + "-" + qty + "-" + date_taken + "-" + supplier_batch;
    const cost = price * stock;
    //check if bird intake will exceed pen capacity
    let foundBirdPen = await BirdPen.findOne({ code: pen });
    let expectedBirds = foundBirdPen.no_of_cage * foundBirdPen.cage_capacity;
    let totalBird = Number(qty) + foundBirdPen.stock;
    if (Number(totalBird) > Number(expectedBirds)) {
      let message = "bird intake as exceeded cage capacity reduce bird !!";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/management/birdintake");
    }
    // also check if the bird intake march the existing bird
    if (type.toLocaleLowerCase() !== foundBirdPen.type.toLocaleLowerCase()) {
      let message =
        "the type of bird you want to put in this pen doesn't march the existing once";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/management/birdintake");
    }
    const createdBirdIntake = await managementService.createBirdIntake(
      batch_code,
      type,
      date_taken,
      stock,
      supplier,
      pen,
      price,
      cost,
      level,
      user.name,
      user.staff_no
    );
    if (createdBirdIntake.batch_code) {
      let message = "Bird intake record  created successfuly";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/management/birdintake");
    } else {
      let message = "something went wrong try again later";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/management/birdintake");
    }
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/management/birdintake");
  }
};
const getAllBirdInTake = async (req, res) => {
  try {
    const allBirdIntakes = await BirdIntake.find();
    return res.json(allBirdIntakes);
  } catch (err) {
    console.log(err.message);
    return res.json(err.message);
  }
};
const createFeedTypes = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const { code, type, Disc } = req.body;
    // check if feed code already exist
    const foundFeedType = await FeedType.findOne({ code: code });
    //if feed if found throw an error
    if (foundFeedType) {
      let message = "Duplicate Feed Type (change the feed code).";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/management/feedtype");
    }
    const createdFeedType = await managementService.createFeedType(
      code,
      type,
      Disc
    );
    if (createdFeedType.code) {
      let message = "feed type created successfuly";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/management/feedtype");
    } else {
      let message = "something went wrong try again later";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/management/feedtype");
    }
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/management/feedtype");
  }
};
const getAllFeedType = async (req, res) => {
  try {
    const allFeedType = await FeedType.find();
    return res.json(allFeedType);
  } catch (err) {
    console.log(err.message);
    return res.json(err.message);
  }
};
const createFeedDetails = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const { code, stock, reorder_l, supplier, unit, price } = req.body;
    // check if details already exist for update
    const foundFeedDetails = await FeedDetails.findOne({
      code: code,
      unit: unit,
    });
    if (foundFeedDetails) {
      const updatedFeedDetails = await managementService.updateFeedDetails(
        code,
        unit,
        stock,
        reorder_l,
        supplier,
        price
      );
      if (updatedFeedDetails.code) {
        let message = "feed details updated successfuly";
        successMsg.push(message);
        req.flash("success", successMsg);
        return res.redirect("/management/feeddetails");
      } else {
        let message = "something went wrong try again later";
        errMsg.push(message);
        req.flash("error", errMsg);
        return res.redirect("/management/feeddetails");
      }
    }
    const createdFeedDetails = await managementService.createFeedDetails(
      code,
      stock,
      reorder_l,
      supplier,
      unit,
      price
    );
    if (createdFeedDetails.stock) {
      let message = "feed details created successfuly";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/management/feeddetails");
    } else {
      let message = "something went wrong try again later";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/management/feeddetails");
    }
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/management/feeddetails");
  }
};
const getAllFeedDetails = async (req, res) => {
  try {
    const allFeedDetails = await FeedDetails.find();
    return res.json(allFeedDetails);
  } catch (err) {
    console.log(err.message);
    return res.json(err.message);
  }
};
const updateCustomer = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const { oldname, name, address, account_balance, account_no, bank } =
      req.body;
    const updatedResult = await managementService.updateCustomer(
      oldname,
      name,
      address,
      account_balance,
      account_no,
      bank
    );
    if (updatedResult.status === "error") {
      errMsg.push(updatedResult.msg);
      req.flash("error", errMsg);
      return res.redirect("/management/updatecustomer");
    }
    if (updatedResult.name) {
      let message = "customer account updated successfuly";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/management/updatecustomer");
    }
    let message = "something went wrong try again later";
    errMsg.push(message);
    req.flash("error", errMsg);
    return res.redirect("/management/updatecustomer");
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/management/updatecustomer");
  }
};
const updateSupplier = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const { supplier_id, name, address, account_balance, account_no, bank } =
      req.body;
    const updatedResult = await managementService.updateSupplier(
      supplier_id,
      name,
      address,
      account_balance,
      account_no,
      bank
    );
    if (updatedResult.status === "error") {
      errMsg.push(updatedResult.msg);
      req.flash("error", errMsg);
      return res.redirect("/management/updatesupplier");
    }
    if (updatedResult.name) {
      let message = "supplier account updated successfuly";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/management/updatesupplier");
    }
    let message = "something went wrong try again later";
    errMsg.push(message);
    req.flash("error", errMsg);
    return res.redirect("/management/updatesupplier");
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/management/updatesupplier");
  }
};
const updateStaff = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const {
      staff_no,
      name,
      level,
      address,
      account_no,
      bank,
      guarantor,
      Expected_salary,
    } = req.body;
    const updatedResult = await managementService.updateStaff(
      staff_no,
      name,
      level,
      address,
      account_no,
      bank,
      guarantor,
      Expected_salary
    );
    if (updatedResult.status === "error") {
      errMsg.push(updatedResult.msg);
      req.flash("error", errMsg);
      return res.redirect("/management/updatestaff");
    }
    if (updatedResult.name) {
      let message = "staff account updated successfuly";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/management/updatestaff");
    }
    let message = "something went wrong try again later";
    errMsg.push(message);
    req.flash("error", errMsg);
    return res.redirect("/management/updatestaff");
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/management/updatestaff");
  }
};
const createBirdPen = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const { code, name, stock, no_of_cage, cage_capacity, batch, type } =
      req.body;
    const date = new Date();
    //check if pen code exist so we can update instead
    const foundBirdPen = await BirdPen.findOne({ code });
    // if (foundBirdPen) {
    //   //if birdpen found update instead
    //   const updatedBirdPen = await managementService.updateBirdPen(
    //     code,
    //     cage_capacity,
    //     stock,
    //     batch,
    //     type
    //   );
    //   if (updatedBirdPen.date) {
    //     let message = "Bird Pen updated successfuly";
    //     successMsg.push(message);
    //     req.flash("success", successMsg);
    //     return res.redirect("/management/birdpen");
    //   } else {
    //     let message = "something went wrong try again later";
    //     errMsg.push(message);
    //     req.flash("error", errMsg);
    //     return res.redirect("/management/birdpen");
    //   }
    // }
    //check if pen already exist if so return an error
    if (foundBirdPen) {
      let message = "Bird Pen already created cannot duplicate !!";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/management/birdpen");
    }
    //check if expected bird exceed current stock
    let expectedBirds = no_of_cage * cage_capacity;
    if (stock > expectedBirds) {
      let message =
        "stock already exceed all cage capacity which shouldn't be, try again! ";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/management/birdpen");
    }
    const createdBirdPen = await managementService.createBirdPen(
      code,
      name,
      date,
      stock,
      no_of_cage,
      cage_capacity,
      batch,
      type
    );
    if (createdBirdPen.date) {
      let message = "Bird Pen created successfuly";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/management/birdpen");
    } else {
      let message = "something went wrong try again later";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/management/birdpen");
    }
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/management/birdpen");
  }
};
const getAllBirdPen = async (req, res) => {
  try {
    const allBirdPen = await BirdPen.find();
    return res.json(allBirdPen);
  } catch (err) {
    console.log(err);
    return err.message;
  }
};
const createBirdRemoved = async (req, res) => {
  let errMsg = [];
  let successMsg = [];
  try {
    const { type_select, qty, type, date_taken, stock, pen, level } = req.body;
    const user = req.user;
    const batch_code = type_select + "-" + qty + "-" + date_taken;
    //check if removed bird is more than stock
    let currentStock = await BirdPen.findOne({ code: pen });
    if (Number(stock) > Number(currentStock.stock)) {
      console.log("got inside the condition");
      let message = "requested removed bird is more than the current stock";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/management/birdremoved");
    }
    // also check if the bird removedtake march the existing bird
    if (type == foundBirdPen.type) {
      let message =
        "the type of bird you want to remove in this pen doesn't march the existing once";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/management/birdintake");
    }
    const createdBirdRemoved = await managementService.createBirdRemoved(
      batch_code,
      type,
      date_taken,
      stock,
      pen,
      level,
      user.name,
      user.staff_no
    );

    if (createdBirdRemoved.batch_code) {
      let message = "Bird removed record  created successfuly";
      successMsg.push(message);
      req.flash("success", successMsg);
      return res.redirect("/management/birdremoved");
    } else {
      let message = "something went wrong try again later";
      errMsg.push(message);
      req.flash("error", errMsg);
      return res.redirect("/management/birdremoved");
    }
  } catch (err) {
    console.log(err.message);
    errMsg.push(err.message);
    req.flash("error", errMsg);
    return res.redirect("/management/birdremoved");
  }
};
const getAllBirdRemoved = async (req, res) => {
  try {
    const allBirdRemoved = await BirdRemoved.find();
    return res.json(allBirdRemoved);
  } catch (err) {
    console.log(err);
    return err.message;
  }
};
const filterBirdIntake = async (req, res) => {
  try {
    const { from, to } = req.params;

    const foundRecord = await BirdIntake.find({
      date_taken: {
        $gte: new Date(from),
        $lte: new Date(to),
      },
    }).sort({ date: 1 });
    if (!foundRecord || foundRecord.length === 0) {
      return res.json([]);
    }
    return res.json(foundRecord);
  } catch (err) {
    console.log(err);
    return res.json(err.message);
  }
};

const filterBirdPen = async (req, res) => {
  try {
    const { from, to } = req.params;

    const foundRecord = await BirdPen.find({
      date: {
        $gte: new Date(from),
        $lte: new Date(to),
      },
    }).sort({ date: 1 });
    if (!foundRecord || foundRecord.length === 0) {
      return res.json([]);
    }
    return res.json(foundRecord);
  } catch (err) {
    console.log(err);
    return res.json(err.message);
  }
};
const filterBirdRemove = async (req, res) => {
  try {
    const { from, to } = req.params;

    const foundRecord = await BirdRemoved.find({
      date_taken: {
        $gte: new Date(from),
        $lte: new Date(to),
      },
    }).sort({ date: 1 });
    if (!foundRecord || foundRecord.length === 0) {
      return res.json([]);
    }
    return res.json(foundRecord);
  } catch (err) {
    console.log(err);
    return res.json(err.message);
  }
};
const filterFeedType = async (req, res) => {
  try {
    const { from, to } = req.params;

    const foundRecord = await FeedType.find({
      createdAt: {
        $gte: new Date(from),
        $lte: new Date(to),
      },
    }).sort({ date: 1 });
    if (!foundRecord || foundRecord.length === 0) {
      return res.json([]);
    }
    return res.json(foundRecord);
  } catch (err) {
    console.log(err);
    return res.json(err.message);
  }
};
const filterFeedDetails = async (req, res) => {
  try {
    const { from, to } = req.params;

    const foundRecord = await FeedDetails.find({
      createdAt: {
        $gte: new Date(from),
        $lte: new Date(to),
      },
    }).sort({ date: 1 });
    if (!foundRecord || foundRecord.length === 0) {
      return res.json([]);
    }
    return res.json(foundRecord);
  } catch (err) {
    console.log(err);
    return res.json(err.message);
  }
};
const filterCustomer = async (req, res) => {
  try {
    const { from, to } = req.params;

    const foundRecord = await Customer.find({
      createdAt: {
        $gte: new Date(from),
        $lte: new Date(to),
      },
    }).sort({ date: 1 });
    if (!foundRecord || foundRecord.length === 0) {
      return res.json([]);
    }
    return res.json(foundRecord);
  } catch (err) {
    console.log(err);
    return res.json(err.message);
  }
};
const filterStaff = async (req, res) => {
  try {
    const { from, to } = req.params;

    const foundRecord = await Staff.find({
      createdAt: {
        $gte: new Date(from),
        $lte: new Date(to),
      },
    }).sort({ date: 1 });
    if (!foundRecord || foundRecord.length === 0) {
      return res.json([]);
    }
    return res.json(foundRecord);
  } catch (err) {
    console.log(err);
    return res.json(err.message);
  }
};
const filterSupplier = async (req, res) => {
  try {
    const { from, to } = req.params;

    const foundRecord = await Supplier.find({
      createdAt: {
        $gte: new Date(from),
        $lte: new Date(to),
      },
    }).sort({ date: 1 });
    if (!foundRecord || foundRecord.length === 0) {
      return res.json([]);
    }
    return res.json(foundRecord);
  } catch (err) {
    console.log(err);
    return res.json(err.message);
  }
};
module.exports = {
  createStaff,
  findStaffByStaffNo,
  getAllStaff,
  searchStaffByName,
  createSupplier,
  getAllSupplier,
  searchSupplierByName,
  createCustomer,
  getAllCustomer,
  searchCustomerByName,
  createBirdIntake,
  getAllBirdInTake,
  createFeedTypes,
  getAllFeedType,
  createFeedDetails,
  getAllFeedDetails,
  updateCustomer,
  updateSupplier,
  updateStaff,
  createBirdPen,
  getAllBirdPen,
  createBirdRemoved,
  getAllBirdRemoved,
  filterBirdIntake,
  filterBirdPen,
  filterBirdRemove,
  filterFeedType,
  filterFeedDetails,
  filterCustomer,
  filterStaff,
  filterSupplier,
};
