<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/global-styles.css" />
    <title>Create Staff Account - Farm Management System</title>
  </head>
  <body>
    <!-- Modern Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="app-logo">Farm Management System</div>
        <div class="user-info">
          <span>Create Staff Account</span>
        </div>
      </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav">
      <div class="breadcrumb-content">
        <a href="/management/homepage" class="breadcrumb-item">Dashboard</a>
        <span class="breadcrumb-separator">›</span>
        <a href="/management/homepage" class="breadcrumb-item">Management</a>
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-item active">Create Staff</span>
      </div>
    </nav>

    <!-- Main Container -->
    <div class="app-container">
      <!-- Alerts -->
      <% if(hasErr){%>
      <div class="alert alert-danger">
        <div>
          <% errMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%> <% if(hasSuccess){%>
      <div class="alert alert-success">
        <div>
          <% successMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%>

      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">👨‍💼 Create Staff Account</h1>
        <p class="page-subtitle">
          Add new staff member to your farm management system
        </p>
        <div class="page-actions">
          <a href="/management/homepage" class="btn btn-secondary btn-back"
            >Back to Dashboard</a
          >
          <a href="/management/check-staff" class="btn btn-outline"
            >View All Staff</a
          >
        </div>
      </div>

      <!-- Staff Form -->
      <div class="form-container">
        <form action="/management/createstaff" method="POST" id="staffForm">
          <!-- Staff Identification -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">🆔 Staff Identification</h3>
            </div>
            <div class="card-body">
              <div class="form-grid">
                <div class="form-group">
                  <label for="staff_no" class="form-label"
                    >🆔 Staff Number</label
                  >
                  <input
                    type="text"
                    class="form-control"
                    name="staff_no"
                    id="staff_no"
                    placeholder="Enter unique staff number"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="level" class="form-label">👔 Staff Level</label>
                  <select
                    name="level"
                    class="form-control form-select"
                    id="level"
                    required
                  >
                    <option value="">Select staff level...</option>
                    <option value="attendant">👷 Attendant</option>
                    <option value="supervisor">👨‍💼 Supervisor</option>
                    <option value="manager">🧑‍💼 Manager</option>
                    <option value="director">👑 Director</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          <!-- Personal Information -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">👤 Personal Information</h3>
            </div>
            <div class="card-body">
              <div class="form-grid">
                <div class="form-group">
                  <label for="name" class="form-label">👤 Full Name</label>
                  <input
                    type="text"
                    class="form-control"
                    name="name"
                    id="name"
                    placeholder="Enter staff full name"
                    required
                  />
                </div>

                <div class="form-group" style="grid-column: 1 / -1">
                  <label for="address" class="form-label"
                    >📍 Home Address</label
                  >
                  <textarea
                    class="form-control"
                    name="address"
                    id="address"
                    placeholder="Enter staff complete address"
                    rows="3"
                    required
                  ></textarea>
                </div>

                <div class="form-group">
                  <label for="guarantor" class="form-label"
                    >🤝 Guarantor Name</label
                  >
                  <input
                    type="text"
                    class="form-control"
                    name="guarantor"
                    id="guarantor"
                    placeholder="Enter guarantor's full name"
                    required
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Financial Information -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">💰 Financial Information</h3>
            </div>
            <div class="card-body">
              <div class="form-grid">
                <div class="form-group">
                  <label for="account_no" class="form-label"
                    >🏦 Account Number</label
                  >
                  <input
                    type="text"
                    class="form-control"
                    name="account_no"
                    id="account_no"
                    placeholder="Enter bank account number"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="bank" class="form-label">🏛️ Bank Name</label>
                  <input
                    type="text"
                    class="form-control"
                    name="bank"
                    id="bank"
                    placeholder="Enter bank name"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="bank" class="form-label"
                    >💰 Expected Salary</label
                  >
                  <input
                    type="text"
                    class="form-control"
                    name="Expected_salary"
                    id="bank"
                    placeholder="Enter bank name"
                    required
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Security Information -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">🔒 Security Information</h3>
            </div>
            <div class="card-body">
              <div class="form-group">
                <label for="password" class="form-label"
                  >🔒 Staff Password</label
                >
                <input
                  type="password"
                  class="form-control"
                  name="password"
                  id="password"
                  placeholder="Enter secure password"
                  required
                />
                <small class="text-muted"
                  >Password will be used for staff login</small
                >
              </div>
            </div>
          </div>

          <!-- Staff Summary -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">📊 Staff Summary</h3>
            </div>
            <div class="card-body">
              <div class="grid grid-4">
                <div class="text-center">
                  <h4 class="text-primary" id="staffNumber">-</h4>
                  <p class="text-muted">Staff Number</p>
                </div>
                <div class="text-center">
                  <h4 class="text-info" id="staffName">-</h4>
                  <p class="text-muted">Staff Name</p>
                </div>
                <div class="text-center">
                  <h4 class="text-warning" id="staffLevel">-</h4>
                  <p class="text-muted">Staff Level</p>
                </div>
                <div class="text-center">
                  <h4 class="text-success" id="bankName">-</h4>
                  <p class="text-muted">Bank</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="d-flex justify-between align-center">
            <div>
              <span class="text-muted">All fields are required</span>
            </div>
            <div class="d-flex gap-2">
              <button type="reset" class="btn btn-secondary">
                🔄 Reset Form
              </button>
              <button type="submit" class="btn btn-success btn-save">
                👨‍💼 Create Staff
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Enhanced JavaScript -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const staffNoInput = document.getElementById("staff_no");
        const nameInput = document.getElementById("name");
        const levelSelect = document.getElementById("level");
        const bankInput = document.getElementById("bank");

        const staffNumberDisplay = document.getElementById("staffNumber");
        const staffNameDisplay = document.getElementById("staffName");
        const staffLevelDisplay = document.getElementById("staffLevel");
        const bankNameDisplay = document.getElementById("bankName");

        function updateDisplays() {
          const staffNo = staffNoInput.value || "-";
          const name = nameInput.value || "-";
          const level =
            levelSelect.options[levelSelect.selectedIndex]?.text || "-";
          const bank = bankInput.value || "-";

          staffNumberDisplay.textContent = staffNo;
          staffNameDisplay.textContent = name;
          staffLevelDisplay.textContent = level.split(" ")[1] || level;
          bankNameDisplay.textContent = bank;
        }

        // Add event listeners
        [staffNoInput, nameInput, levelSelect, bankInput].forEach((input) => {
          input.addEventListener("input", updateDisplays);
          input.addEventListener("change", updateDisplays);
        });

        // Form submission enhancement
        const form = document.getElementById("staffForm");
        form.addEventListener("submit", function (e) {
          const submitBtn = form.querySelector('button[type="submit"]');
          submitBtn.innerHTML = '<span class="loading"></span> Creating...';
          submitBtn.disabled = true;
        });

        // Initialize displays
        updateDisplays();
      });
    </script>
  </body>
</html>
