<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/global-styles.css" />
    <title>Create Feed Type - Farm Management System</title>
  </head>
  <body>
    <!-- Modern Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="app-logo">Farm Management System</div>
        <div class="user-info">
          <span>Create Feed Type</span>
        </div>
      </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav">
      <div class="breadcrumb-content">
        <a href="/management/homepage" class="breadcrumb-item">Dashboard</a>
        <span class="breadcrumb-separator">›</span>
        <a href="/management/homepage" class="breadcrumb-item">Management</a>
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-item active">Create Feed Type</span>
      </div>
    </nav>

    <!-- Main Container -->
    <div class="app-container">
      <!-- Alerts -->
      <% if(hasErr){%>
      <div class="alert alert-danger">
        <div>
          <% errMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%> <% if(hasSuccess){%>
      <div class="alert alert-success">
        <div>
          <% successMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%>

      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">🌾 Create Feed Type</h1>
        <p class="page-subtitle">
          Define new feed categories and specifications
        </p>
        <div class="page-actions">
          <a href="/management/homepage" class="btn btn-secondary btn-back"
            >Back to Dashboard</a
          >
          <a href="/management/check-feedtype" class="btn btn-outline"
            >View All Feed Types</a
          >
        </div>
      </div>

      <!-- Feed Type Form -->
      <div class="form-container">
        <form action="/management/feedtype" method="POST" id="feedTypeForm">
          <!-- Feed Type Information -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">🏷️ Feed Type Information</h3>
            </div>
            <div class="card-body">
              <div class="form-grid">
                <div class="form-group">
                  <label for="code" class="form-label">🏷️ Feed Code</label>
                  <input
                    type="text"
                    class="form-control"
                    name="code"
                    id="code"
                    placeholder="Enter unique feed code (e.g., ST001, GR002)"
                    required
                    style="text-transform: uppercase"
                  />
                  <small class="text-muted"
                    >Use a unique identifier for this feed type</small
                  >
                </div>

                <div class="form-group">
                  <label for="type" class="form-label">🌾 Feed Type Name</label>
                  <input
                    type="text"
                    class="form-control"
                    name="type"
                    id="type"
                    placeholder="Enter feed type name (e.g., Starter Feed, Grower Feed)"
                    required
                  />
                </div>

                <div class="form-group" style="grid-column: 1 / -1">
                  <label for="Disc" class="form-label">📝 Description</label>
                  <textarea
                    class="form-control"
                    name="Disc"
                    id="Disc"
                    placeholder="Enter detailed description of the feed type, its purpose, and usage instructions"
                    rows="4"
                    required
                  ></textarea>
                </div>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="d-flex justify-between align-center">
            <div>
              <span class="text-muted">All fields are required</span>
            </div>
            <div class="d-flex gap-2">
              <button type="reset" class="btn btn-secondary">
                🔄 Reset Form
              </button>
              <button type="submit" class="btn btn-success btn-save">
                🌾 Create Feed Type
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Enhanced JavaScript -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const codeInput = document.getElementById("code");

        // Auto-uppercase feed code
        codeInput.addEventListener("input", function () {
          this.value = this.value.toUpperCase();
        });

        // Form submission enhancement
        const form = document.getElementById("feedTypeForm");
        form.addEventListener("submit", function (e) {
          const submitBtn = form.querySelector('button[type="submit"]');
          submitBtn.innerHTML = '<span class="loading"></span> Creating...';
          submitBtn.disabled = true;
        });

        // Add visual feedback for form fields
        const formControls = document.querySelectorAll(".form-control");
        formControls.forEach((control) => {
          control.addEventListener("focus", function () {
            this.parentElement.style.transform = "scale(1.02)";
            this.parentElement.style.transition = "transform 0.2s ease";
          });

          control.addEventListener("blur", function () {
            this.parentElement.style.transform = "scale(1)";
          });
        });
      });
    </script>
  </body>
</html>
