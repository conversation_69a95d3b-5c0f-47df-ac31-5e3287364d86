const mongoose = require("mongoose");

const dailyProductionRecordSchema = new mongoose.Schema(
  {
    pen: {
      type: String,
      required: true,
    },
    date: {
      type: Date,
      required: true,
    },
    stock_code: {
      type: Number,
      required: true,
    },
    mortality: {
      type: Number,
      required: true,
    },
    feed_code: {
      type: String,
      required: true,
    },
    feed_unit: {
      type: String,
      required: true,
    },
    feed_qty: {
      type: Number,
      required: true,
    },
    big_eggs: {
      type: Number,
      required: true,
    },
    b_egg_price: {
      type: Number,
      required: true,
    },
    small_eggs: {
      type: Number,
      required: true,
    },
    s_egg_price: {
      type: Number,
      required: true,
    },
    broken_eggs_crate: {
      type: Number,
      required: true,
    },
    broken_egg_price: {
      type: Number,
      required: true,
    },
    total_egg_price: {
      type: Number,
      required: true,
    },
    prod_percent: {
      type: Number,
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

module.exports = mongoose.model(
  "DailyProductionRecord",
  dailyProductionRecordSchema
);
