const managementBtn = document.getElementById("management-btn");
const recordBtn = document.getElementById("record-btn");
const reportBtn = document.getElementById("report-btn");
const managementMenu = document.getElementById("management-menu");
const recordMenu = document.getElementById("record-menu");
const reportMenu = document.getElementById("report-menu");
const deleteDailyProdBtn = document.getElementById(
  "delete_daily_production_btn"
);
const deleteMedBtn = document.getElementById("delete_medication_btn");
const deleteSuppBtn = document.getElementById("delete_supplies_btn");
const deleteSalesBtn = document.getElementById("delete_sales_btn");
const deleteDailyProdInput = document.getElementById("delete_daily_production");
const deleteMedInput = document.getElementById("delete_medication");
const deleteSuppInput = document.getElementById("delete_supplies");
const deleteSalesInput = document.getElementById("delete_sales");

managementBtn.addEventListener("click", (e) => {
  e.preventDefault();
  recordMenu.style.display = "none";
  reportMenu.style.display = "none";
  managementMenu.style.display = "block";
});

recordBtn.addEventListener("click", (e) => {
  e.preventDefault();
  managementMenu.style.display = "none";
  reportMenu.style.display = "none";
  recordMenu.style.display = "block";
});

reportBtn.addEventListener("click", (e) => {
  e.preventDefault();
  managementMenu.style.display = "none";
  recordMenu.style.display = "none";
  reportMenu.style.display = "block";
});

deleteDailyProdBtn.addEventListener("click", (e) => {
  e.preventDefault();
  if (deleteDailyProdInput.style.display === "block") {
    deleteDailyProdInput.style.display = "none";
  }
  deleteDailyProdInput.style.display = "block";
});

// deleteMedBtn.addEventListener("click", (e) => {
//   e.preventDefault();
//   deleteDailyProdInput.style.display = "none";
//   deleteSalesInput.style.display = "none";
//   deleteSuppInput.style.display = "none";
//   deleteMedInput.style.display = "block";
// });

// deleteSalesBtn.addEventListener("click", (e) => {
//   e.preventDefault();
//   deleteMedInput.style.display = "none";
//   deleteDailyProdInput.style.display = "none";
//   deleteSuppInput.style.display = "none";
//   deleteSalesInput.style.display = "block";
// });

// deleteSuppBtn.addEventListener("click", (e) => {
//   e.preventDefault();
//   deleteMedInput.style.display = "none";
//   deleteDailyProdInput.style.display = "none";
//   deleteSalesInput.style.display = "none";
//   deleteSuppInput.style.display = "block";
// });
