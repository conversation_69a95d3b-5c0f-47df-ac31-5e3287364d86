<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link
      href="https://fonts.googleapis.com/icon?family=Material+Icons"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="/stylesheets/global-styles.css" />
    <script src="/javascript/check-birdremoved.js" defer></script>
    <title>Bird Removed Records - Farm Management System</title>
  </head>
  <body>
    <!-- Modern Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="app-logo">Farm Management System</div>
        <div class="user-info">
          <span>Bird Removed Records</span>
        </div>
      </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav no-print">
      <div class="breadcrumb-content">
        <a href="/management/homepage" class="breadcrumb-item">Dashboard</a>
        <span class="breadcrumb-separator">›</span>
        <a href="/management/homepage" class="breadcrumb-item">Management</a>
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-item active">Bird Removed Records</span>
      </div>
    </nav>

    <!-- Main Container -->
    <div class="app-container">
      <!-- Alerts -->
      <% if(hasErr){%>
      <div class="alert alert-danger">
        <div>
          <% errMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%> <% if(hasSuccess){%>
      <div class="alert alert-success">
        <div>
          <% successMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%>

      <!-- Page Header -->
      <div class="page-header no-print">
        <h1 class="page-title">🚪 Bird Removed Records</h1>
        <p class="page-subtitle">Track all bird removals and transfers</p>
        <div class="page-actions">
          <a href="/management/homepage" class="btn btn-secondary btn-back"
            >Back to Dashboard</a
          >
          <a href="/management/birdremoved" class="btn btn-success btn-add"
            >Add New Removal</a
          >
          <button id="refreshBtn" class="btn btn-primary btn-refresh">
            Refresh Data
          </button>
          <button id="printBtn" class="btn btn-outline btn-print no-print">
            Print Report
          </button>
        </div>
      </div>

      <!-- Data Table Container -->
      <div class="table-container">
        <div class="table-header no-print">
          <h3 class="table-title">
            Desire's Poultry🚪 Bird Removed Records For: <%= DATE %>
          </h3>
          <div class="table-actions">
            <div id="loading-indicator" class="d-none">
              <span class="loading"></span> Loading...
            </div>
            <span id="record-count" class="text-muted">Loading records...</span>
          </div>
        </div>

        <table class="data-table" id="main-table">
          <thead>
            <tr>
              <th>Date Removed</th>
              <th>Batch Code</th>
              <th>Bird Type</th>
              <th>Number of Birds</th>
              <th>Pen</th>
              <th>Level</th>
              <th>Attendant Name</th>
              <th>Staff No</th>
            </tr>
          </thead>
          <tbody id="data-table-body">
            <tr>
              <td colspan="8" class="text-center p-4">
                <div class="loading"></div>
                <span class="ml-2">Loading bird removal records...</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </body>
</html>
