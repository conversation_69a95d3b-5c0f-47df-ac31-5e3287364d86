<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/global-styles.css" />
    <script src="/javascript/check-birdintake.js" defer></script>
    <title>Bird Intake Records - Farm Management System</title>
  </head>
  <body>
    <!-- Modern Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="app-logo">Farm Management System</div>
        <div class="user-info">
          <span>Bird Intake Records</span>
        </div>
      </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav no-print">
      <div class="breadcrumb-content">
        <a href="/management/homepage" class="breadcrumb-item">Dashboard</a>
        <span class="breadcrumb-separator">›</span>
        <a href="/management/homepage" class="breadcrumb-item">Management</a>
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-item active">Bird Intake Records</span>
      </div>
    </nav>

    <!-- Main Container -->
    <div class="app-container">
      <!-- Alerts -->
      <% if(hasErr){%>
      <div class="alert alert-danger">
        <div>
          <% errMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%> <% if(hasSuccess){%>
      <div class="alert alert-success">
        <div>
          <% successMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%>

      <!-- Page Header -->
      <div class="page-header no-print">
        <h1 class="page-title">🐔 Bird Intake Records</h1>
        <p class="page-subtitle">
          View and manage all bird intake transactions
        </p>
        <div class="page-actions">
          <a href="/management/homepage" class="btn btn-secondary btn-back"
            >Back to Dashboard</a
          >
          <a href="/management/birdintake" class="btn btn-success btn-add"
            >Add New Intake</a
          >
          <button id="refreshBtn" class="btn btn-primary btn-refresh">
            Refresh Data
          </button>
          <button id="printBtn" class="btn btn-outline btn-print no-print">
            Print Report
          </button>
        </div>
      </div>

      <!-- Date Range Filter -->
      <!-- <div class="card mb-4 no-print">
        <div class="card-header">
          <h3 class="card-title">📅 Filter by Date Range</h3>
        </div>
        <div class="card-body">
          <form id="dateFilterForm" class="form-grid">
            <div class="form-group">
              <label for="from" class="form-label">📅 From Date</label>
              <input
                type="date"
                class="form-control"
                name="from"
                id="from"
                required
              />
            </div>
            <div class="form-group">
              <label for="to" class="form-label">📅 To Date</label>
              <input
                type="date"
                class="form-control"
                name="to"
                id="to"
                required
              />
            </div>
            <div class="form-group d-flex align-center">
              <button
                type="submit"
                class="btn btn-primary btn-search"
                id="submit"
              >
                🔍 Generate Report
              </button>
            </div>
          </form>
        </div>
      </div> -->

      <!-- Data Table Container -->
      <div class="table-container">
        <div class="table-header no-print">
          <h3 class="table-title">
            Desire's Poultry Bird Intake Records For: <%= DATE %>
          </h3>
          <div class="table-actions">
            <div id="loading-indicator" class="d-none">
              <span class="loading"></span> Loading...
            </div>
            <span id="record-count" class="text-muted">Loading records...</span>
          </div>
        </div>

        <table class="data-table" id="main-table">
          <thead>
            <tr>
              <th>Date Taken</th>
              <th>Batch Code</th>
              <th>Bird Type</th>
              <th>Number of Birds</th>
              <th>Supplier</th>
              <th>Pen</th>
              <th>Price per Bird</th>
              <th>Total Cost</th>
              <th>Level</th>
              <th>Attendant Name</th>
              <th>Staff No</th>
            </tr>
          </thead>
          <tbody id="data-table-body">
            <!-- Data will be loaded here via JavaScript -->
            <tr>
              <td colspan="11" class="text-center p-4">
                <div class="loading"></div>
                <span class="ml-2">Loading bird intake records...</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </body>
</html>
