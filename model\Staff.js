const mongoose = require("mongoose");

const staffSchema = new mongoose.Schema(
  {
    staff_no: {
      type: String,
      unique: true,
      required: true,
    },
    level: {
      type: String,
      required: true,
    },
    name: {
      type: String,
      required: true,
    },
    address: {
      type: String,
      required: true,
    },
    account_no: {
      type: Number,
      required: true,
    },
    bank: {
      type: String,
      required: true,
    },
    guarantor: {
      type: String,
      required: true,
    },
    password: {
      type: String,
      required: true,
    },
    expected_salary: {
      type: Number,
    },
  },
  {
    timestamps: true,
  }
);

module.exports = mongoose.model("Staff", staffSchema);
