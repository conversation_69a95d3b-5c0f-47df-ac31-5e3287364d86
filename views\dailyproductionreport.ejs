<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/global-styles.css" />
    <script src="/javascript/dailyproductionreport.js" defer></script>
    <title>Daily Production Report - Farm Management System</title>
  </head>
  <body>
    <!-- Modern Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="app-logo">Farm Management System</div>
        <div class="user-info">
          <span>Daily Production Report</span>
        </div>
      </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav no-print">
      <div class="breadcrumb-content">
        <a href="/management/homepage" class="breadcrumb-item">Dashboard</a>
        <span class="breadcrumb-separator">›</span>
        <a href="/management/homepage" class="breadcrumb-item">Reports</a>
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-item active">Daily Production</span>
      </div>
    </nav>

    <!-- Main Container -->
    <div class="app-container">
      <!-- Alerts -->
      <% if(hasErr){%>
      <div class="alert alert-danger">
        <div>
          <% errMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%> <% if(hasSuccess){%>
      <div class="alert alert-success">
        <div>
          <% successMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%>

      <!-- Page Header -->
      <div class="page-header no-print">
        <h1 class="page-title">📊 Daily Production Report</h1>
        <p class="page-subtitle">
          Comprehensive daily production analytics and insights
        </p>
        <div class="page-actions">
          <a href="/management/homepage" class="btn btn-secondary btn-back"
            >Back to Dashboard</a
          >
          <a href="/record/dailyproduction" class="btn btn-success btn-add"
            >Add New Record</a
          >
          <button id="printBtn" class="btn btn-outline btn-print no-print">
            Print Report
          </button>
        </div>
      </div>

      <!-- Date Range Filter -->
      <div class="card mb-4 no-print">
        <div class="card-header">
          <h3 class="card-title">📅 Filter by Date Range</h3>
        </div>
        <div class="card-body">
          <form id="dateFilterForm" class="form-grid">
            <div class="form-group">
              <label for="from" class="form-label">📅 From Date</label>
              <input
                type="date"
                class="form-control"
                name="from"
                id="from"
                required
              />
            </div>
            <div class="form-group">
              <label for="to" class="form-label">📅 To Date</label>
              <input
                type="date"
                class="form-control"
                name="to"
                id="to"
                required
              />
            </div>
            <div class="form-group d-flex align-center">
              <button
                type="submit"
                class="btn btn-primary btn-search"
                id="submit"
              >
                🔍 Generate Report
              </button>
            </div>
          </form>
        </div>
      </div>
      <!-- Report Data Table -->
      <div class="table-container" id="printableSection">
        <div class="table-header">
          <h3 class="table-title">
            Desire's Poultry Daily Production Report For: <%= DATE %>
          </h3>
          <div class="table-actions no-print">
            <span class="text-muted"
              ><%= dailyproduction.length %> records found</span
            >
          </div>
        </div>

        <table class="data-table" id="main-table">
          <thead>
            <tr>
              <th>Date</th>
              <th>Pen</th>
              <th>Stock Code</th>
              <th>Mortality</th>
              <th>Feed Code</th>
              <th>Feed Unit</th>
              <th>Feed Qty</th>
              <th>Big Eggs</th>
              <th>B_Eggs P</th>
              <th>Small Eggs</th>
              <th>S_Eggs P</th>
              <th>broken Eggs</th>
              <th>broken egg p</th>
              <th>Total Price</th>
              <th>Production %</th>
            </tr>
          </thead>
          <tbody>
            <% if(dailyproduction && dailyproduction.length > 0) { %> <%
            dailyproduction.forEach((record, index) => { %>
            <tr
              style="
                animation: fadeIn 0.3s ease-in-out <%= index * 0.1 %>s both;
              "
            >
              <td><%= new Date(record.date).toLocaleDateString() %></td>
              <td>
                <span class="status-indicator status-info"
                  ><%= record.pen %></span
                >
              </td>
              <td><strong><%= record.stock_code %></strong></td>
              <td>
                <span
                  class="<%= record.mortality > 0 ? 'text-danger' : 'text-success' %>"
                  ><%= record.mortality %></span
                >
              </td>
              <td><%= record.feed_code %></td>
              <td><%= record.feed_unit %></td>
              <td><%= record.feed_qty %></td>
              <td><%= record.big_eggs %></td>
              <td>
                <strong
                  >₦<%= Number(record.b_egg_price).toLocaleString() %></strong
                >
              </td>
              <td><%= record.small_eggs %></td>
              <td>
                <strong
                  >₦<%= Number(record.s_egg_price).toLocaleString() %></strong
                >
              </td>
              <td><%= record.broken_eggs_crate %></td>
              <td>
                <strong
                  >₦<%= Number(record.broken_egg_price).toLocaleString()
                  %></strong
                >
              </td>
              <td><strong><%= record.total_egg_price %></strong></td>
              <td>
                <span
                  class="status-indicator <%= Number(record.prod_percent) >= 80 ? 'status-success' : Number(record.prod_percent) >= 60 ? 'status-warning' : 'status-danger' %>"
                >
                  <%= Number(record.prod_percent).toFixed(1) %>%
                </span>
              </td>
            </tr>
            <% }) %> <% } else { %>
            <tr>
              <td colspan="15" class="text-center p-4">
                <div class="text-muted">
                  <h5>📭 No Records Found</h5>
                  <p>
                    No daily production records found for the selected date
                    range.
                  </p>
                  <a
                    href="/record/dailyproduction"
                    class="btn btn-primary btn-sm"
                  >
                    ➕ Create First Record
                  </a>
                </div>
              </td>
            </tr>
            <% } %>
          </tbody>
        </table>

        <!-- Summary Table -->
        <div id="display-tr" class="mt-4"></div>
      </div>
    </div>

    <!-- Enhanced JavaScript -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const fromInput = document.getElementById("from");
        const toInput = document.getElementById("to");

        // Set default date range (last 7 days)
        const today = new Date();
        const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

        toInput.value = today.toISOString().split("T")[0];
        fromInput.value = lastWeek.toISOString().split("T")[0];

        // Print button functionality
        //   const printBtn = document.getElementById("printBtn");
        //   if (printBtn) {
        //     printBtn.addEventListener("click", function () {
        //       window.print();
        //     });
        //   }
      });
    </script>

    <!-- Add CSS animations -->
    <style>
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .status-indicator {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-weight: 600;
      }

      .status-info {
        background: #e3f2fd;
        color: #1565c0;
      }
      .status-success {
        background: #e8f5e8;
        color: #2e7d32;
      }
      .status-warning {
        background: #fff3e0;
        color: #ef6c00;
      }
      .status-danger {
        background: #ffebee;
        color: #c62828;
      }
    </style>
  </body>
</html>
