const mongoose = require('mongoose');

const suppliesRecordSchema = new mongoose.Schema({
  date: {
    type: Date,
    required: true
  },
  supplier: {
    type: String,
    required: true
  },
  feed_type: {
    type: String,
    required: true
  },
  feed_unit: {
    type: String,
    required: true
  },
  stock_bf: {
    type: Number,
    required: true
  },
  price: {
    type: Number,
    required: true
  },
  quantity: {
    type: Number,
    required: true
  },
  amount: {
    type: Number,
    required: true
  },
  remark: {
    type: String,
    required: true
  },
  stock_cf: {
    type: Number,
    required: true
  },
  staff_name: {
    type: String,
    required: true
  },
  staff_no: {
    type: String,
    required: true
  }
}, {
  timestamps: true
});

module.exports = mongoose.model('SuppliesRecord', suppliesRecordSchema);
