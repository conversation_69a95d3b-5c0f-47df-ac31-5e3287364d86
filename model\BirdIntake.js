const mongoose = require("mongoose");

const birdIntakeSchema = new mongoose.Schema(
  {
    batch_code: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      required: true,
    },
    date_taken: {
      type: Date,
      required: true,
    },
    no_of_bird: {
      type: Number,
      required: true,
    },
    supplier: {
      type: String,
      required: true,
    },
    pen: {
      type: String,
      required: true,
    },
    price: {
      type: Number,
      required: true,
    },
    cost: {
      type: Number,
      required: true,
    },
    level: {
      type: String,
      required: true,
    },
    attenedant_name: {
      type: String,
      required: true,
    },
    attenedant_staff_no: {
      type: String,
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

module.exports = mongoose.model("BirdIntake", birdIntakeSchema);
