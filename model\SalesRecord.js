const mongoose = require("mongoose");

const salesRecordSchema = new mongoose.Schema(
  {
    date: {
      type: Date,
      required: true,
    },
    customer: {
      type: String,
      required: true,
    },
    big_egg_crate: {
      type: String,
    },
    big_egg_price: {
      type: Number,
    },
    small_egg_crate: {
      type: Number,
    },
    small_egg_price: {
      type: Number,
    },
    broken_egg_crate: {
      type: Number,
    },
    broken_egg_price: {
      type: Number,
    },
    total_price: {
      type: Number,
      required: true,
    },
    remark: {
      type: String,
      required: true,
    },
    staff_name: {
      type: String,
      required: true,
    },
    staff_no: {
      type: String,
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

module.exports = mongoose.model("SalesRecord", salesRecordSchema);
