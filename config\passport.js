const passport = require("passport");
const localStrategy = require("passport-local").Strategy;
const argon = require("argon2");
const Staff = require("../model/Staff");

passport.use(
  "staff.login",
  new localStrategy(
    {
      usernameField: "staffno",
      passwordField: "password",
      passReqToCallback: true,
    },
    async (req, staffno, password, done) => {
      let errMsg = [];
      let successMsg = [];
      try {
        const foundStaff = await Staff.findOne({ staff_no: staffno });
        if (!foundStaff) {
          let message = "no staff found with this staff_no";
          errMsg.push(message);
          return done(null, false, req.flash("error", errMsg));
        }
        let verifyPassword = await argon.verify(foundStaff.password, password);
        if (!verifyPassword) {
          let message = "incorrect password";
          errMsg.push(message);
          return done(null, false, req.flash("error", errMsg));
        } else {
          let message = "login successful";
          successMsg.push(message);
          return done(null, foundStaff, req.flash("success", successMsg));
        }
      } catch (err) {
        console.log(err);
        return done(null, false, err.message);
      }
    }
  )
);

passport.serializeUser(function (user, done) {
  done(null, user._id);
});
passport.deserializeUser(async function (id, done) {
  const foundStaff = await Staff.findById(id);
  return done(null, foundStaff);
});

module.exports = passport;
