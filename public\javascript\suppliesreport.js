const fromInput = document.getElementById("from");
const toInput = document.getElementById("to");
const submitBtn = document.getElementById("submit");
const searchDiv = document.getElementById("searchdiv");
const printableSection = document.getElementById("printableSection");
const displayTr = document.getElementById("display-tr");
const printBtn = document.getElementById("printbtn");
const hideSec = document.getElementById("main-table");
const mainTableBody = document.querySelector("#main-table tbody");

submitBtn.addEventListener("click", (e) => {
  e.preventDefault();
  let payload = {
    date_from: fromInput.value,
    date_to: toInput.value,
  };
  fetch("/report/supplies", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(payload),
  })
    .then((res) => res.json())
    .then((data) => {
      let htmlContent = "";
      console.log("got outside:", data);
      if (data.length > 0) {
        console.log("got inside:", data);
        data.forEach((item) => {
          htmlContent += `<tr>
            <td>${new Date(item.date).toLocaleDateString()}</td>
            <td>${item.supplier}</td>
            <td>${item.feed_type}</td>
            <td>${item.feed_unit}</td>
            <td>${item.stock_bf}</td>
             <td>${Number(item.quantity).toLocaleString()}</td>
            <td>₦${Number(item.price).toLocaleString()}</td>
            <td>₦${Number(item.amount).toLocaleString()}</td>
            <td>₦${Number(item.stock_cf).toLocaleString()}</td>
            <td>₦${item.staff_name}</td>
            <td>${item.staff_no}</td>
             <td>${item.remark}</td>
          </tr>`;
        });
      } else {
        htmlContent = `<tr>
          <td colspan="15" class="text-center p-4">
            <div class="text-muted">
              <h5>📭 No Records Found</h5>
              <p>No daily production records found for the selected date range.</p>
            </div>
          </td>
        </tr>`;
      }
      mainTableBody.innerHTML = htmlContent;
    });
});
printBtn.addEventListener("click", (e) => {
  e.preventDefault();
  searchDiv.style.display = "none";
  printBtn.style.display = "none";
  window.print();
});
