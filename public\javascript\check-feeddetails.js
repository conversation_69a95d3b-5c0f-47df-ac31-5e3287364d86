const refreshBtn = document.getElementById("refreshBtn");
const dataTableBody = document.getElementById("data-table-body");
const printBtn = document.getElementById("printbtn");
const searchDiv = document.getElementById("searchdiv");

// Function to load feed details data
function loadFeedDetailsData() {
  fetch("/management/allfeeddetails")
    .then((res) => res.json())
    .then((data) => {
      let htmlContent = "";
      if (data && data.length > 0) {
        data.forEach((record) => {
          const dateFormatted = record.updatedAt ? new Date(record.updatedAt).toLocaleDateString() : 'N/A';
          // Check stock level and add warning if below reorder level
          const stockWarning = Number(record.stock) <= Number(record.reorder_L) ? 
            ' style="background-color: #ffebee; color: #c62828;"' : '';
          
          htmlContent += `
            <tr${stockWarning}>
              <td>${record.code}</td>
              <td>${record.stock} ${record.unit}</td>
              <td>${record.reorder_L} ${record.unit}</td>
              <td>${record.supplier}</td>
              <td>${record.unit}</td>
              <td>₦${record.price}</td>
              <td>${dateFormatted}</td>
            </tr>
          `;
        });
      } else {
        htmlContent = `
          <tr>
            <td colspan="7" style="text-align: center; padding: 20px;">
              No feed details records found
            </td>
          </tr>
        `;
      }
      dataTableBody.innerHTML = htmlContent;
    })
    .catch((error) => {
      console.error("Error loading feed details data:", error);
      dataTableBody.innerHTML = `
        <tr>
          <td colspan="7" style="text-align: center; padding: 20px; color: red;">
            Error loading data. Please try again.
          </td>
        </tr>
      `;
    });
}

// Load data when page loads
document.addEventListener("DOMContentLoaded", loadFeedDetailsData);

// Refresh button event listener
refreshBtn.addEventListener("click", (e) => {
  e.preventDefault();
  loadFeedDetailsData();
});

// Print button event listener
printBtn.addEventListener("click", (e) => {
  e.preventDefault();
  searchDiv.style.display = "none";
  printBtn.style.display = "none";
  window.print();
  // Restore elements after printing
  setTimeout(() => {
    searchDiv.style.display = "block";
    printBtn.style.display = "block";
  }, 1000);
});
