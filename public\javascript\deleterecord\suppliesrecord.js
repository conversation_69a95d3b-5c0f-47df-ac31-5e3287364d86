const fromInput = document.getElementById("from");
const toInput = document.getElementById("to");
const submitBtn = document.getElementById("submit");
const dateFilterForm = document.getElementById("dateFilterForm");
const recordContainer = document.getElementById("record-container");

// Listen for filter form submit
if (dateFilterForm) {
  dateFilterForm.addEventListener("submit", function (e) {
    e.preventDefault();
    let payload = {
      date_from: fromInput.value,
      date_to: toInput.value,
    };
    // Fetch filtered records from the server
    fetch("/report/supplies", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    })
      .then((res) => res.json())
      .then((data) => {
        // Build filtered record rows
        let html = `
          <div class='record-header' id='record-header'>
                <p>Date</p>
                <p>Supplier</p>
                <p>Feed Type</p>
                <p>Feed Unit</p>
                <p>Stock Before</p>
                <p>Quantity</p>
                <p>Price per Unit</p>
                <p>Total Amount</p>
                <p>Stock After</p>
                <p>Staff Name</p>
                <p>Staff No</p>
                <p>Remarks</p>
                <span style="width: 48px"></span>
          </div>
        `;
        if (data.length > 0) {
          data.forEach(function (record) {
            html += `
              <div class='record-row'>
                <p>${new Date(record.date).toLocaleDateString()}</p>
                <p>${record.supplier}</p>                 
                <p>${record.feed_type}</p>                 
                <p>${record.feed_unit}</p>                 
                <p>${record.stock_bf}</p>                 
                <p>${record.quantity}</p>                 
                <p>${record.price}</p>                 
                <p>${record.amount}</p>                 
                <p>${record.stock_cf}</p>                 
                <p>${record.remark}</p>                 
                <p>${record.staff_name}</p>                 
                <p>${record.staff_no}</p>                 
                 <a href="/deleterecord/suppliesrecord/<%= record._id %>" >               
          <i class="material-icons" style="font-size: 32px; color: red" >delete</i>                 
              </div>                 
            `;
          });
        } else {
          html += `<p style='text-align:center'>no record found</p>`;
        }
        recordContainer.innerHTML = html;
      })
      .catch((err) => {
        recordContainer.innerHTML = `<p style='text-align:center;color:red'>Error loading records</p>`;
      });
  });
}
