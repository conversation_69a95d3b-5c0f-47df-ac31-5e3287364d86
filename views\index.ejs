<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/global-styles.css" />
    <title>
      Desires Poultry Management System - Professional Poultry Management
    </title>
  </head>
  <body class="login-page">
    <!-- Background with farm imagery -->
    <div class="login-background">
      <div class="login-overlay"></div>
    </div>

    <!-- Main Login Container -->
    <div class="login-container">
      <!-- Left Side - Branding & Features -->
      <div class="login-branding">
        <div class="brand-header">
          <div class="brand-logo">🐔</div>
          <h1 class="brand-title">Desires Poultry Management System</h1>
          <p class="brand-subtitle">
            Professional Poultry Farm Management Solution
          </p>
        </div>

        <div class="features-list">
          <div class="feature-item">
            <div class="feature-icon">📊</div>
            <div class="feature-content">
              <h3>Production Analytics</h3>
              <p>
                Track daily production, feed consumption, and performance
                metrics
              </p>
            </div>
          </div>

          <div class="feature-item">
            <div class="feature-icon">💰</div>
            <div class="feature-content">
              <h3>Financial Management</h3>
              <p>
                Monitor sales, expenses, and profitability with detailed reports
              </p>
            </div>
          </div>

          <div class="feature-item">
            <div class="feature-icon">🏠</div>
            <div class="feature-content">
              <h3>Farm Operations</h3>
              <p>
                Manage bird intake, pen allocation, and inventory efficiently
              </p>
            </div>
          </div>

          <div class="feature-item">
            <div class="feature-icon">💊</div>
            <div class="feature-content">
              <h3>Health Monitoring</h3>
              <p>Track medication, mortality, and veterinary treatments</p>
            </div>
          </div>
        </div>

        <div class="brand-footer">
          <p>Trusted by poultry farmers for efficient farm management</p>
        </div>
      </div>

      <!-- Right Side - Login Form -->
      <div class="login-form-section">
        <div class="login-form-container">
          <div class="login-header">
            <h2>Welcome Back</h2>
            <p>Sign in to access your farm management dashboard</p>
          </div>

          <!-- Alerts -->
          <% if(hasErr){%>
          <div class="alert alert-danger">
            <div class="alert-icon">⚠️</div>
            <div class="alert-content">
              <% errMsg.forEach(message =>{ %>
              <p class="mb-0"><%= message%></p>
              <% })%>
            </div>
          </div>
          <%}%> <% if(hasSuccess){%>
          <div class="alert alert-success">
            <div class="alert-icon">✅</div>
            <div class="alert-content">
              <% successMsg.forEach(message =>{ %>
              <p class="mb-0"><%= message%></p>
              <% })%>
            </div>
          </div>
          <%}%>

          <!-- Login Form -->
          <form
            action="/management/login"
            method="POST"
            id="loginForm"
            class="login-form"
          >
            <div class="form-group">
              <label for="staffno" class="form-label">👨‍💼 Staff Number</label>
              <input
                type="text"
                class="form-control"
                name="staffno"
                id="staffno"
                placeholder="Enter your staff number"
                required
                autocomplete="username"
              />
            </div>

            <div class="form-group">
              <label for="password" class="form-label">🔒 Password</label>
              <input
                type="password"
                class="form-control"
                name="password"
                id="password"
                placeholder="Enter your password"
                required
                autocomplete="current-password"
              />
            </div>

            <button type="submit" class="btn btn-primary btn-login">
              <span class="btn-text">🚀 Sign In to Dashboard</span>
              <span class="btn-loading" style="display: none">
                <span class="loading"></span> Signing In...
              </span>
            </button>
          </form>

          <div class="login-footer">
            <div class="security-note">
              <div class="security-icon">🔐</div>
              <p>Your data is protected with enterprise-grade security</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced Styling -->
    <style>
      .login-page {
        margin: 0;
        padding: 0;
        min-height: 100vh;
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        position: relative;
        overflow-x: hidden;
      }

      .login-background {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="farm" patternUnits="userSpaceOnUse" width="100" height="100"><rect width="100" height="100" fill="%23f8f9fa" opacity="0.1"/><circle cx="20" cy="20" r="2" fill="%23ffffff" opacity="0.3"/><circle cx="80" cy="80" r="1.5" fill="%23ffffff" opacity="0.2"/></pattern></defs><rect width="100%" height="100%" fill="url(%23farm)"/></svg>')
          repeat;
        z-index: -2;
      }

      .login-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          135deg,
          rgba(102, 126, 234, 0.8) 0%,
          rgba(118, 75, 162, 0.8) 100%
        );
        z-index: -1;
      }

      .login-container {
        display: flex;
        min-height: 100vh;
        max-width: 1400px;
        margin: 0 auto;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      }

      .login-branding {
        flex: 1;
        padding: 3rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        display: flex;
        flex-direction: column;
        justify-content: center;
        position: relative;
        overflow: hidden;
      }

      .login-branding::before {
        content: "";
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></svg>')
          repeat;
        animation: float 20s linear infinite;
        z-index: 0;
      }

      @keyframes float {
        0% {
          transform: translateX(0) translateY(0) rotate(0deg);
        }
        100% {
          transform: translateX(-50px) translateY(-50px) rotate(360deg);
        }
      }

      .brand-header {
        text-align: center;
        margin-bottom: 3rem;
        position: relative;
        z-index: 1;
      }

      .brand-logo {
        font-size: 4rem;
        margin-bottom: 1rem;
        animation: bounce 2s ease-in-out infinite;
      }

      @keyframes bounce {
        0%,
        20%,
        50%,
        80%,
        100% {
          transform: translateY(0);
        }
        40% {
          transform: translateY(-10px);
        }
        60% {
          transform: translateY(-5px);
        }
      }

      .brand-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
      }

      .brand-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        font-weight: 300;
      }

      .features-list {
        position: relative;
        z-index: 1;
      }

      .feature-item {
        display: flex;
        align-items: center;
        margin-bottom: 2rem;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        backdrop-filter: blur(10px);
        transition: transform 0.3s ease;
      }

      .feature-item:hover {
        transform: translateX(10px);
      }

      .feature-icon {
        font-size: 2rem;
        margin-right: 1rem;
        min-width: 60px;
        text-align: center;
      }

      .feature-content h3 {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 0.25rem;
      }

      .feature-content p {
        font-size: 0.9rem;
        opacity: 0.8;
        margin: 0;
      }

      .brand-footer {
        text-align: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
        position: relative;
        z-index: 1;
      }

      .login-form-section {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem;
        background: #ffffff;
      }

      .login-form-container {
        width: 100%;
        max-width: 400px;
      }

      .login-header {
        text-align: center;
        margin-bottom: 2rem;
      }

      .login-header h2 {
        font-size: 2rem;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 0.5rem;
      }

      .login-header p {
        color: #718096;
        font-size: 1rem;
      }

      .alert {
        display: flex;
        align-items: center;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1.5rem;
        border: none;
      }

      .alert-danger {
        background: #fed7d7;
        color: #c53030;
      }

      .alert-success {
        background: #c6f6d5;
        color: #2f855a;
      }

      .alert-icon {
        margin-right: 0.75rem;
        font-size: 1.2rem;
      }

      .login-form .form-group {
        margin-bottom: 1.5rem;
      }

      .login-form .form-label {
        display: block;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
      }

      .login-form .form-control {
        width: 100%;
        padding: 0.875rem 1rem;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: #ffffff;
      }

      .login-form .form-control:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        transform: translateY(-2px);
      }

      .btn-login {
        width: 100%;
        padding: 1rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      .btn-login:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
      }

      .btn-login:active {
        transform: translateY(0);
      }

      .login-footer {
        margin-top: 2rem;
        text-align: center;
      }

      .security-note {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #718096;
        font-size: 0.85rem;
      }

      .security-icon {
        margin-right: 0.5rem;
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .login-container {
          flex-direction: column;
        }

        .login-branding {
          padding: 2rem;
          min-height: 40vh;
        }

        .brand-title {
          font-size: 2rem;
        }

        .features-list {
          display: none;
        }
      }

      /* Loading animation */
      .loading {
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #ffffff;
        animation: spin 1s ease-in-out infinite;
      }

      @keyframes spin {
        to {
          transform: rotate(360deg);
        }
      }
    </style>

    <!-- Enhanced JavaScript -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const form = document.getElementById("loginForm");
        const submitBtn = form.querySelector('button[type="submit"]');
        const btnText = submitBtn.querySelector(".btn-text");
        const btnLoading = submitBtn.querySelector(".btn-loading");

        form.addEventListener("submit", function (e) {
          btnText.style.display = "none";
          btnLoading.style.display = "inline-flex";
          submitBtn.disabled = true;
        });

        // Add focus animations to form inputs
        const inputs = form.querySelectorAll(".form-control");
        inputs.forEach((input) => {
          input.addEventListener("focus", function () {
            this.parentElement.style.transform = "scale(1.02)";
          });

          input.addEventListener("blur", function () {
            this.parentElement.style.transform = "scale(1)";
          });
        });
      });
    </script>
  </body>
</html>
