<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/global-styles.css" />
    <link
      href="https://fonts.googleapis.com/icon?family=Material+Icons"
      rel="stylesheet"
    />
    <script
      src="/javascript/deleterecord/dailyproductionrecord.js"
      defer
    ></script>
    <title>Delete DailyProduction Record</title>
  </head>
  <body>
    <!-- Modern Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="app-logo">Farm Management System</div>
        <div class="user-info">
          <span> Delete Dailyproduction Record</span>
        </div>
      </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav">
      <div class="breadcrumb-content">
        <a href="/management/homepage" class="breadcrumb-item"
          >Management Dashboard</a
        >
        <span class="breadcrumb-separator">›</span>
        <a href="/deleterecord" class="breadcrumb-item"
          >Delete Record Dashboard</a
        >
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-item active">Record Details</span>
      </div>
    </nav>

    <!-- Alerts -->
    <% if(hasErr){%>
    <div class="alert alert-danger">
      <div>
        <% errMsg.forEach(message =>{ %>
        <p class="mb-0"><%= message%></p>
        <% })%>
      </div>
    </div>
    <%}%> <% if(hasSuccess){%>
    <div class="alert alert-success">
      <div>
        <% successMsg.forEach(message =>{ %>
        <p class="mb-0"><%= message%></p>
        <% })%>
      </div>
    </div>
    <%}%>

    <!-- Page Header -->
    <div class="page-header">
      <h1 class="page-title">📊 Delete Dailyproduction Records</h1>
      <p class="page-subtitle">
        Delete Dailyproduction record from record list ...
      </p>
      <div class="page-actions">
        <a href="/deleterecord/" class="btn btn-secondary btn-back"
          >Back to Delete Dashboard</a
        >
      </div>
    </div>

    <!-- Date Range Filter -->
    <div class="card mb-4 no-print">
      <div class="card-header">
        <h3 class="card-title">📅 Filter by Date Range</h3>
      </div>
      <div class="card-body">
        <form id="dateFilterForm" class="form-grid">
          <div class="form-group">
            <label for="from" class="form-label">📅 From Date</label>
            <input
              type="date"
              class="form-control"
              name="from"
              id="from"
              required
            />
          </div>
          <div class="form-group">
            <label for="to" class="form-label">📅 To Date</label>
            <input
              type="date"
              class="form-control"
              name="to"
              id="to"
              required
            />
          </div>
          <div class="form-group d-flex align-center">
            <button
              type="submit"
              class="btn btn-primary btn-search"
              id="submit"
            >
              🔍 Generate Report
            </button>
          </div>
        </form>
      </div>
    </div>
    <!-- record list container -->
    <div class="record-container" id="record-container">
      <div class="record-header" id="record-header">
        <p>Date</p>
        <p>Pen</p>
        <p>Stock Code</p>
        <p>Mortality</p>
        <p>Feed Code</p>
        <p>Feed Unit</p>
        <p>Feed Qty</p>
        <p>Big Eggs</p>
        <p>B_Eggs Price</p>
        <p>Small Eggs</p>
        <p>S_Eggs Price</p>
        <p>broken Eggs</p>
        <p>Broken Egg Price</p>
        <p>Total Price</p>
        <p>Production %</p>
        <span style="width: 48px"></span>
      </div>
      <% if(hasDailyProductionRecord){ %> <%
      dailyProductionRecord.forEach(function(record){ %>
      <div class="record-row">
        <p><%= new Date(record.date).toLocaleDateString() %></p>
        <p><%= record.pen %></p>
        <p><%= record.stock_code %></p>
        <p><%= record.mortality %></p>
        <p><%= record.feed_code %></p>
        <p><%= record.feed_unit %></p>
        <p><%= record.feed_qty %></p>
        <p><%= record.big_eggs %></p>
        <p><%= record.b_egg_price %></p>
        <p><%= record.small_eggs %></p>
        <p><%= record.s_egg_price %></p>
        <p><%= record.broken_eggs_crate %></p>
        <p><%= record.broken_egg_price %></p>
        <p><%= record.total_egg_price %></p>
        <p><%= record.prod_percent %></p>
        <a href="/deleterecord/dailyproductionrecord/<%= record._id %>"
          ><i class="material-icons" style="font-size: 32px; color: red"
            >delete</i
          ></a
        >
      </div>
      <% }) %> <% }else{ %>
      <p style="text-align: center">no record found</p>
      <% } %>
    </div>

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Attach confirmation to all delete links
        document.querySelectorAll(".record-row a").forEach(function (link) {
          link.addEventListener("click", function (e) {
            const confirmed = confirm(
              "Are you sure you want to delete this bird intake record? This action cannot be undone."
            );
            if (!confirmed) {
              e.preventDefault();
            }
          });
        });
      });
    </script>
    <style>
      .page-header {
        margin: 40px 20px;
      }
      .record-container {
        margin: 30px auto;
        max-width: 100vw;
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        padding: 0 0 20px 0;
        overflow-x: auto;
      }
      .record-header,
      .record-row {
        display: grid;
        grid-template-columns: repeat(
          16,
          minmax(110px, 1fr)
        ); /* 15 data + 1 action */
        align-items: center;
        gap: 6px;
        padding: 8px 8px;
        font-size: 0.97rem;
      }
      .record-header {
        background: #f5f7fa;
        font-weight: 600;
        border-bottom: 2px solid #e0e0e0;
        color: #1565c0;
        font-size: 1rem;
      }

      .record-row {
        border-bottom: 1px solid #e0e0e0;
        transition: background 0.2s;
        font-size: 0.96rem;
      }

      .record-row:hover {
        background: #f0f4fa;
      }
      .record-row p,
      .record-header p {
        margin: 0;
        padding: 0 2px;
        word-break: break-word;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .record-row a {
        display: flex;
        justify-content: center;
        align-items: center;
        text-decoration: none;
      }
      @media (max-width: 1200px) {
        .record-header,
        .record-row {
          font-size: 0.92rem;
          grid-template-columns: repeat(16, minmax(90px, 1fr));
          padding: 6px 2px;
        }
      }
      @media (max-width: 900px) {
        .record-header,
        .record-row {
          font-size: 0.92rem;
          padding: 8px 4px;
        }
      }
      @media (max-width: 800px) {
        .record-header,
        .record-row {
          font-size: 0.85rem;
          grid-template-columns: repeat(16, minmax(70px, 1fr));
          padding: 4px 1px;
        }
        .record-container {
          padding: 0 0 10px 0;
        }
      }
      @media (max-width: 600px) {
        .record-header,
        .record-row {
          grid-template-columns: repeat(11, minmax(80px, 1fr));
          font-size: 0.85rem;
        }
        .record-container {
          padding: 0 0 10px 0;
        }
      }
    </style>
  </body>
</html>
