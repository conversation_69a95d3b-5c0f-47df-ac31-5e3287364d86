const express = require("express");
const dotenv = require("dotenv");
//const databaseConnect = require("./config/databaseconfig");
const managemantRoutes = require("./router/management");
const recordRoutes = require("./router/records");
const reportRoutes = require("./router/reports");
const path = require("path");
const session = require("express-session");
//const redisStore = require("connect-redis").default;
//const redis = require("ioredis");
const passport = require("./config/passport");
const mongoose = require("mongoose");
const flash = require("connect-flash");
const mongoStore = require("connect-mongo"); // Import the connect-mongo module
const deleteRoutes = require("./router/deleterecord");

const app = express();

// set up database

dotenv.config();
// connect to database
let DB_URL = process.env.DB_url; // Changed to match your .env file
mongoose
  .connect(DB_URL)
  .then(() => console.log("connected to mongoDB"))
  .catch((err) => console.log(err.message));

// databaseConnect
//   .initialize()
//   .then(() => {
//     console.log(`connected to database successfuly!!`);
//   })
//   .catch((err) => {
//     throw new Error(err.message);
//   });
// json middleware
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
// config ejs view engine
app.set("view engine", "ejs");
app.use(express.static(path.join(__dirname, "public")));

//set up passport and session middleware
// const redisClient = new redis();
// app.use(
//   session({
//     store: new redisStore({ client: redisClient }),
//     secret: "omotehinse",
//     resave: false,
//     saveUninitialized: false,
//   })
// );
app.use(
  session({
    secret: process.env.SESSION_SECRET || "Danilosessionsecret",
    resave: false,
    saveUninitialized: false,
    store: mongoStore.create({ mongoUrl: DB_URL }), // Make sure this uses the same variable
    cookie: { maxAge: 180 * 60 * 1000 }, // 3 hours
  })
);
app.use(flash());
app.use(passport.initialize());
app.use(passport.session());
// set authentication middleware
app.use((req, res, next) => {
  res.locals.login = req.isAuthenticated();
  next();
});
// set management routes
app.use("/management", managemantRoutes);
// set Record routes
app.use("/record", recordRoutes);
// set report routes
app.use("/report", reportRoutes);
app.use("/deleterecord", deleteRoutes);
app.get("/", (req, res) => {
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("index", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
  });
});

const port = process.env.PORT || 1999;

app.listen(port, () => {
  console.log(`app is running on port ${port}`);
});
