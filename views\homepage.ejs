<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/global-styles.css" />
    <script src="/javascript/homepage.js" defer></script>
    <title>Desires Poultry System - Dashboard</title>
  </head>
  <body>
    <!-- Modern Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="app-logo">Desires Poultry Management System</div>
        <div class="user-info">
          <div class="user-avatar">
            <%= profile.name.charAt(0).toUpperCase() %>
          </div>
          <div>
            <div style="font-weight: 600"><%= profile.name %></div>
            <div style="font-size: 0.875rem; opacity: 0.8">
              <%= profile.level %>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav">
      <div class="breadcrumb-content">
        <a href="/management/homepage" class="breadcrumb-item active"
          >Dashboard</a
        >
      </div>
    </nav>

    <!-- Main Container -->
    <div class="app-container">
      <!-- Alerts -->
      <% if(hasErr){%>
      <div class="alert alert-danger">
        <div>
          <% errMsg.forEach(function(message) { %>
          <p class="mb-0"><%= message %></p>
          <% }) %>
        </div>
      </div>
      <%}%> <% if(hasSuccess){%>
      <div class="alert alert-success">
        <div>
          <% successMsg.forEach(function(message){ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%} %> <% if(reorderStatus){ %>
      <div class="page-header alert alert-danger" style="display: block">
        <h1 class="page-title">NOTICE !!!</h1>
        <% reorderMsg.forEach(function(msg){ %>
        <p class="page-subtitle"><%= msg %></p>
        <% }) %>
      </div>
      <% } %>
      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">Welcome back, <%= profile.name %>!</h1>
        <p class="page-subtitle">
          Manage your farm operations efficiently with our comprehensive
          dashboard
        </p>
      </div>
      <!-- Dashboard Navigation Cards -->
      <div class="grid grid-3 mb-4">
        <div class="card" id="management-card" style="cursor: pointer">
          <div class="card-header">
            <h3 class="card-title">🏢 Management</h3>
          </div>
          <div class="card-body">
            <p>Manage staff, customers, suppliers, and farm infrastructure</p>
            <div class="d-flex justify-between align-center">
              <span class="text-muted">Click to expand</span>
              <span class="btn-icon">→</span>
            </div>
          </div>
        </div>

        <div class="card" id="record-card" style="cursor: pointer">
          <div class="card-header">
            <h3 class="card-title">📊 Records</h3>
          </div>
          <div class="card-body">
            <p>Create daily production, sales, and medication records</p>
            <div class="d-flex justify-between align-center">
              <span class="text-muted">Click to expand</span>
              <span class="btn-icon">→</span>
            </div>
          </div>
        </div>

        <div class="card" id="report-card" style="cursor: pointer">
          <div class="card-header">
            <h3 class="card-title">📈 Reports</h3>
          </div>
          <div class="card-body">
            <p>Generate comprehensive reports and analytics</p>
            <div class="d-flex justify-between align-center">
              <span class="text-muted">Click to expand</span>
              <span class="btn-icon">→</span>
            </div>
          </div>
        </div>
      </div>
      <!-- Management Menu -->
      <div id="management-menu" class="d-none management-menu-container">
        <!-- Create Records Section -->
        <div class="card mb-3">
          <div class="card-header">
            <h3 class="card-title">➕ Create Records</h3>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6 mb-2">
                <a href="/management/birdintake" class="btn btn-outline w-100">
                  🐔 Create Bird Intake
                </a>
              </div>
              <div class="col-md-6 mb-2">
                <a href="/management/birdremoved" class="btn btn-outline w-100">
                  🚪 Create Bird Removal
                </a>
              </div>
              <div class="col-md-6 mb-2">
                <a href="/management/feedtype" class="btn btn-outline w-100">
                  🌾 Create Feed Type
                </a>
              </div>
              <div class="col-md-6 mb-2">
                <a href="/management/birdpen" class="btn btn-outline w-100">
                  🏠 Create Bird Pen
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- View Records Section -->
        <div class="card mb-3">
          <div class="card-header">
            <h3 class="card-title">👁️ View Records</h3>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6 mb-2">
                <a
                  href="/management/check-birdintake"
                  class="btn btn-outline w-100"
                >
                  📋 View Bird Intake
                </a>
              </div>
              <div class="col-md-6 mb-2">
                <a
                  href="/management/check-birdremoved"
                  class="btn btn-outline w-100"
                >
                  📋 View Bird Removals
                </a>
              </div>
              <div class="col-md-6 mb-2">
                <a
                  href="/management/check-feedtype"
                  class="btn btn-outline w-100"
                >
                  📋 View Feed Types
                </a>
              </div>
              <div class="col-md-6 mb-2">
                <a
                  href="/management/check-feeddetails"
                  class="btn btn-outline w-100"
                >
                  📋 View Feed Details
                </a>
              </div>
              <div class="col-md-6 mb-2">
                <a
                  href="/management/check-birdpen"
                  class="btn btn-outline w-100"
                >
                  📋 View Bird Pens
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions Section -->
        <div class="card mb-3">
          <div class="card-header">
            <h3 class="card-title">⚡ Quick Actions</h3>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6 mb-2">
                <a href="/record/dailyproduction" class="btn btn-primary w-100">
                  📅 Daily Production
                </a>
              </div>
              <div class="col-md-6 mb-2">
                <a href="/record/medication" class="btn btn-primary w-100">
                  💊 Medication Record
                </a>
              </div>
              <div class="col-md-6 mb-2">
                <a href="/record/supplies" class="btn btn-primary w-100">
                  📦 Supplies Record
                </a>
              </div>
              <div class="col-md-6 mb-2">
                <a href="/record/sales" class="btn btn-primary w-100">
                  💰 Sales Record
                </a>
              </div>
              <div class="col-md-6 mb-2">
                <a
                  href="/report/dailyproduction"
                  class="btn btn-secondary w-100"
                >
                  📊 Production Report
                </a>
              </div>
              <div class="col-md-6 mb-2">
                <a href="/report/sales" class="btn btn-secondary w-100">
                  💰 Sales Report
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Director Only Sections -->
        <% if(directorStatus){%>
        <!-- Create Accounts Section -->
        <div class="card mb-3" style="border-left: 4px solid #28a745">
          <div class="card-header bg-success text-white">
            <h3 class="card-title">👑 Director - Create Accounts</h3>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6 mb-2">
                <a
                  href="/management/createcustomer"
                  class="btn btn-success w-100"
                >
                  👥 Create Customer
                </a>
              </div>
              <div class="col-md-6 mb-2">
                <a
                  href="/management/createsupplier"
                  class="btn btn-success w-100"
                >
                  🏭 Create Supplier
                </a>
              </div>
              <div class="col-md-6 mb-2">
                <a href="/management/createstaff" class="btn btn-success w-100">
                  👨‍💼 Create Staff
                </a>
              </div>
              <div class="col-md-6 mb-2">
                <a href="/management/feeddetails" class="btn btn-outline w-100">
                  📦 Create/Update Feed Details
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Update Accounts Section -->
        <div class="card mb-3" style="border-left: 4px solid #ffc107">
          <div class="card-header bg-warning text-dark">
            <h3 class="card-title">👑 Director - Update Accounts</h3>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6 mb-2">
                <a
                  href="/management/updatecustomer"
                  class="btn btn-warning w-100"
                >
                  ✏️ Update Customer
                </a>
              </div>
              <div class="col-md-6 mb-2">
                <a
                  href="/management/updatesupplier"
                  class="btn btn-warning w-100"
                >
                  ✏️ Update Supplier
                </a>
              </div>
              <div class="col-md-6 mb-2">
                <a href="/management/updatestaff" class="btn btn-warning w-100">
                  ✏️ Update Staff
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- View Accounts Section -->
        <div class="card mb-3" style="border-left: 4px solid #17a2b8">
          <div class="card-header bg-info text-white">
            <h3 class="card-title">👑 Director - View Accounts</h3>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6 mb-2">
                <a
                  href="/management/check-customers"
                  class="btn btn-info w-100"
                >
                  👥 View Customers
                </a>
              </div>
              <div class="col-md-6 mb-2">
                <a
                  href="/management/check-suppliers"
                  class="btn btn-info w-100"
                >
                  🏭 View Suppliers
                </a>
              </div>
              <div class="col-md-6 mb-2">
                <a href="/management/check-staff" class="btn btn-info w-100">
                  👨‍💼 View Staff
                </a>
              </div>
              <div class="col-md-6 mb-2">
                <a href="/deleterecord/" class="btn btn-info w-100">
                  📊 Delete Records
                </a>
              </div>
            </div>
          </div>
        </div>
        <%}%>
      </div>
      <!-- Records Menu -->
      <div id="record-menu" class="d-none">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">📊 Farm Records Management</h3>
          </div>
          <div class="card-body">
            <div class="grid grid-2 gap-3">
              <a href="/record/dailyproduction" class="btn btn-primary"
                >📅 Create Daily Production Record</a
              >
              <a href="/record/medication" class="btn btn-primary"
                >💊 Create Medication Record</a
              >
              <a href="/record/supplies" class="btn btn-primary"
                >📦 Create Supplies Record</a
              >
              <a href="/record/sales" class="btn btn-primary"
                >💰 Create Sales Record</a
              >
            </div>
          </div>
        </div>
      </div>

      <!-- Reports Menu -->
      <div id="report-menu" class="d-none">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">📈 Reports & Analytics</h3>
          </div>
          <div class="card-body">
            <div class="grid grid-2 gap-3">
              <a href="/report/dailyproduction" class="btn btn-secondary"
                >📊 Daily Production Report</a
              >
              <a href="/report/medication" class="btn btn-secondary"
                >💊 Medication Report</a
              >
              <a href="/report/supplies" class="btn btn-secondary"
                >📦 Supplies Report</a
              >
              <a href="/report/sales" class="btn btn-secondary"
                >💰 Sales Report</a
              >
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Simple CSS for Management Menu -->
    <style>
      /* Management Menu Container */
      .management-menu-container {
        width: 100%;
        padding: 1rem 0;
      }

      /* Ensure buttons have proper spacing and hover effects */
      .management-menu-container .btn {
        transition: all 0.3s ease;
        margin-bottom: 0.5rem;
      }

      .management-menu-container .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      /* Responsive adjustments */
      @media (max-width: 768px) {
        .management-menu-container .row .col-md-6 {
          margin-bottom: 0.5rem;
        }
      }
    </style>

    <!-- Enhanced JavaScript for smooth interactions -->
    <script>
      // Enhanced homepage interactions
      document.addEventListener("DOMContentLoaded", function () {
        const managementCard = document.getElementById("management-card");
        const recordCard = document.getElementById("record-card");
        const reportCard = document.getElementById("report-card");

        const managementMenu = document.getElementById("management-menu");
        const recordMenu = document.getElementById("record-menu");
        const reportMenu = document.getElementById("report-menu");

        // Hide all menus initially
        function hideAllMenus() {
          managementMenu.classList.add("d-none");
          recordMenu.classList.add("d-none");
          reportMenu.classList.add("d-none");
        }

        // Show management menu
        managementCard.addEventListener("click", function () {
          hideAllMenus();
          managementMenu.classList.remove("d-none");
          managementMenu.scrollIntoView({ behavior: "smooth" });
        });

        // Show record menu
        recordCard.addEventListener("click", function () {
          hideAllMenus();
          recordMenu.classList.remove("d-none");
          recordMenu.scrollIntoView({ behavior: "smooth" });
        });

        // Show report menu
        reportCard.addEventListener("click", function () {
          hideAllMenus();
          reportMenu.classList.remove("d-none");
          reportMenu.scrollIntoView({ behavior: "smooth" });
        });

        // Add hover effects
        [managementCard, recordCard, reportCard].forEach((card) => {
          card.addEventListener("mouseenter", function () {
            this.style.transform = "translateY(-5px)";
            this.style.boxShadow = "var(--shadow-lg)";
          });

          card.addEventListener("mouseleave", function () {
            this.style.transform = "translateY(0)";
            this.style.boxShadow = "var(--shadow-sm)";
          });
        });
      });
    </script>
  </body>
</html>
