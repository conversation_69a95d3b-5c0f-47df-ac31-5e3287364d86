<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/global-styles.css" />
    <script src="/javascript/check-staff.js" defer></script>
    <title>Staff Management - Director Only</title>
  </head>
  <body>
    <!-- Modern Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="app-logo">Farm Management System</div>
        <div class="user-info">
          <span>👑 Director - Staff Management</span>
        </div>
      </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav no-print">
      <div class="breadcrumb-content">
        <a href="/management/homepage" class="breadcrumb-item">Dashboard</a>
        <span class="breadcrumb-separator">›</span>
        <a href="/management/homepage" class="breadcrumb-item">Management</a>
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-item active">Staff Records</span>
      </div>
    </nav>

    <!-- Main Container -->
    <div class="app-container">
      <!-- Alerts -->
      <% if(hasErr){%>
      <div class="alert alert-danger">
        <div>
          <% errMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%> <% if(hasSuccess){%>
      <div class="alert alert-success">
        <div>
          <% successMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%>

      <!-- Page Header -->
      <div
        class="page-header no-print"
        style="border-left-color: var(--info-color)"
      >
        <h1 class="page-title">👨‍💼 Staff Management</h1>
        <p class="page-subtitle">
          Director-only access to staff records and employment data
        </p>
        <div class="page-actions">
          <a href="/management/homepage" class="btn btn-secondary btn-back"
            >Back to Dashboard</a
          >
          <a href="/management/createstaff" class="btn btn-success btn-add"
            >Add New Staff</a
          >
          <a href="/management/updatestaff" class="btn btn-warning btn-edit"
            >Update Staff</a
          >
          <button id="refreshBtn" class="btn btn-primary btn-refresh">
            Refresh Data
          </button>
        </div>
      </div>

      <!-- Data Table Container -->
      <div class="table-container">
        <div
          class="table-header no-print"
          style="
            background: linear-gradient(135deg, var(--info-color), #138496);
          "
        >
          <h3 class="table-title">Staff Directory</h3>
          <div class="table-actions">
            <div id="loading-indicator" class="d-none">
              <span class="loading"></span> Loading...
            </div>
            <span id="record-count" class="text-muted">Loading records...</span>
          </div>
        </div>

        <table class="data-table" id="main-table">
          <thead>
            <tr>
              <th>Staff Number</th>
              <th>Name</th>
              <th>Level</th>
              <th>Address</th>
              <th>Account Number</th>
              <th>Bank</th>
              <th>Expected Salary</th>
              <th>Guarantor</th>
              <th>Created Date</th>
            </tr>
          </thead>
          <tbody id="data-table-body">
            <tr>
              <td colspan="9" class="text-center p-4">
                <div class="loading"></div>
                <span class="ml-2">Loading staff data...</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </body>
</html>
