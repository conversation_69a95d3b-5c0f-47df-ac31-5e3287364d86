/* General Styles */
body {
  font-family: "Arial", sans-serif;
  margin: 0;
  padding: 0;
  background-color: #f9f9f9;
  color: #333;
  box-sizing: border-box;
}

/* Navigation Bar */
.top_nav {
  background-color: #4caf50;
  color: white;
  padding: 15px;
  text-align: center;
  font-size: 24px;
  font-weight: bold;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Alerts */
.alert {
  margin: 20px auto;
  padding: 15px;
  max-width: 800px;
  border-radius: 5px;
  font-size: 14px;
  text-align: center;
}

.alert .error {
  color: #721c24;
}

.alert .success {
  color: #155724;
}

.alert-danger {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
}

.alert-success {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
}

/* Form Container */
form {
  max-width: 800px;
  margin: 30px auto;
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

form h6.topic {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  color: #4caf50;
  margin-bottom: 20px;
  animation: fadeIn 1.5s ease-in-out;
}

/* Input Fields */
.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
}

.form-control,
.form-select {
  width: 100%;
  padding: 10px;
  margin-bottom: 20px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s ease-in-out;
}

.form-control:focus,
.form-select:focus {
  border-color: #4caf50;
  outline: none;
}

/* Button Styles */
.btn {
  display: block;
  width: 100%;
  padding: 10px;
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease-in-out, transform 0.2s ease;
}

.btn:hover {
  background-color: #45a049;
  transform: scale(1.05);
}

/* Animations */
@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  form {
    padding: 15px;
  }

  .btn {
    font-size: 14px;
  }
}
