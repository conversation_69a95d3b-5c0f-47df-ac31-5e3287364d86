<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/global-styles.css" />
    <title>Sales Record - Farm Management System</title>
  </head>
  <body>
    <!-- Modern Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="app-logo">Farm Management System</div>
        <div class="user-info">
          <span>Sales Record</span>
        </div>
      </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav">
      <div class="breadcrumb-content">
        <a href="/management/homepage" class="breadcrumb-item">Dashboard</a>
        <span class="breadcrumb-separator">›</span>
        <a href="/management/homepage" class="breadcrumb-item">Records</a>
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-item active">Sales Record</span>
      </div>
    </nav>

    <!-- Main Container -->
    <div class="app-container">
      <!-- Alerts -->
      <% if(hasErr){%>
      <div class="alert alert-danger">
        <div>
          <% errMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%> <% if(hasSuccess){%>
      <div class="alert alert-success">
        <div>
          <% successMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%>

      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">💰 Sales Record</h1>
        <p class="page-subtitle">Record egg sales and customer transactions</p>
        <div class="page-actions">
          <a href="/management/homepage" class="btn btn-secondary btn-back"
            >Back to Dashboard</a
          >
          <a href="/report/sales" class="btn btn-outline">View Sales Reports</a>
        </div>
      </div>
      <!-- Sales Record Form -->
      <div class="form-container">
        <form action="/record/sales" method="POST" id="salesForm">
          <!-- Customer Information Section -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">👥 Customer Information</h3>
            </div>
            <div class="card-body">
              <div class="form-group">
                <label for="customer" class="form-label"
                  >👤 Customer Name</label
                >
                <input
                  type="text"
                  class="form-control"
                  name="customer"
                  id="customer"
                  placeholder="Enter customer name"
                  required
                />
              </div>
            </div>
          </div>

          <!-- Product Details Section -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">🥚 Product Details</h3>
            </div>
            <div class="card-body">
              <div class="form-grid">
                <div class="form-group">
                  <label for="no_of_crate" class="form-label"
                    >📦 Big Egg Crates</label
                  >
                  <input
                    type="number"
                    class="form-control"
                    name="big_egg_crate"
                    id="big_egg_crate"
                    placeholder="Enter number of crates"
                    min="1"
                  />
                </div>
                <div class="form-group">
                  <label for="no_of_crate" class="form-label"
                    >📦 Small Egg Crates</label
                  >
                  <input
                    type="number"
                    class="form-control"
                    name="small_egg_crate"
                    id="small_egg_crate"
                    placeholder="Enter number of crates"
                    min="1"
                  />
                </div>
                <div class="form-group">
                  <label for="no_of_crate" class="form-label"
                    >📦 Broken Egg Crates</label
                  >
                  <input
                    type="number"
                    class="form-control"
                    name="broken_egg_crate"
                    id="broken_egg_crate"
                    placeholder="Enter number of crates"
                    min="1"
                  />
                </div>
                <div class="form-group">
                  <label for="no_of_crate" class="form-label"
                    >📦 Big Egg Price (per crate)</label
                  >
                  <input
                    type="number"
                    class="form-control"
                    name="big_egg_price"
                    id="big_egg_price"
                    placeholder="Enter number of crates"
                    min="1"
                  />
                </div>

                <div class="form-group">
                  <label for="no_of_crate" class="form-label">
                    Small Egg Price (per crate)</label
                  >
                  <input
                    type="number"
                    class="form-control"
                    name="small_egg_price"
                    id="small_egg_price"
                    placeholder="Enter number of crates"
                    min="1"
                  />
                </div>

                <div class="form-group">
                  <label for="no_of_crate" class="form-label">
                    Broken Egg Price (per crate)</label
                  >
                  <input
                    type="number"
                    class="form-control"
                    name="broken_egg_price"
                    id="broken_egg_price"
                    placeholder="Enter number of crates"
                    min="1"
                  />
                </div>
                <div class="form-group">
                  <label for="remark" class="form-label">📝 Remarks</label>
                  <textarea
                    class="form-control"
                    name="remark"
                    id="remark"
                    placeholder="Enter any additional remarks or notes"
                    rows="3"
                  ></textarea>
                </div>
              </div>
            </div>
          </div>

          <!-- Sales Summary -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">📊 Sales Summary</h3>
            </div>
            <div class="card-body">
              <div class="grid grid-3">
                <div class="text-center">
                  <h4 class="text-primary" id="totalCrates">0</h4>
                  <p class="text-muted">Total Crates</p>
                </div>
                <div class="text-center">
                  <h4 class="text-success" id="pricePerCrate">₦0.00</h4>
                  <p class="text-muted">Price per Crate</p>
                </div>
                <div class="text-center">
                  <h4 class="text-success" id="totalAmount">₦0.00</h4>
                  <p class="text-muted">Total Amount</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="d-flex justify-between align-center">
            <div>
              <span class="text-muted"
                >All fields are required except remarks</span
              >
            </div>
            <div class="d-flex gap-2">
              <button type="reset" class="btn btn-secondary">
                🔄 Reset Form
              </button>
              <button type="submit" class="btn btn-success btn-save">
                💰 Record Sale
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Enhanced JavaScript for real-time calculations -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const bigCrateInput = document.getElementById("big_egg_crate");
        const smallCrateInput = document.getElementById("small_egg_crate");
        const brokenCrateInput = document.getElementById("broken_egg_crate");
        const bigPriceInput = document.getElementById("big_egg_price");
        const smallPriceInput = document.getElementById("small_egg_price");
        const brokenPriceInput = document.getElementById("broken_egg_price");

        const totalCratesDisplay = document.getElementById("totalCrates");
        const pricePerCrateDisplay = document.getElementById("pricePerCrate");
        const totalAmountDisplay = document.getElementById("totalAmount");

        function calculateTotals() {
          const bigCrates = parseFloat(bigCrateInput.value) || 0;
          const smallCrates = parseFloat(smallCrateInput.value) || 0;
          const brokenCrates = parseFloat(brokenCrateInput.value) || 0;
          const bigPrice = parseFloat(bigPriceInput.value) || 0;
          const smallPrice = parseFloat(smallPriceInput.value) || 0;
          const brokenPrice = parseFloat(brokenPriceInput.value) || 0;

          const totalCrates = bigCrates + smallCrates + brokenCrates;
          const totalAmount =
            bigCrates * bigPrice +
            smallCrates * smallPrice +
            brokenCrates * brokenPrice;

          // Optionally, show average price per crate
          const pricePerCrate =
            totalCrates > 0 ? totalAmount / totalCrates : 0;

          totalCratesDisplay.textContent = totalCrates.toLocaleString();
          pricePerCrateDisplay.textContent = new Intl.NumberFormat("en-NG", {
            style: "currency",
            currency: "NGN",
          }).format(pricePerCrate);
          totalAmountDisplay.textContent = new Intl.NumberFormat("en-NG", {
            style: "currency",
            currency: "NGN",
          }).format(totalAmount);
        }

        [
          bigCrateInput,
          smallCrateInput,
          brokenCrateInput,
          bigPriceInput,
          smallPriceInput,
          brokenPriceInput,
        ].forEach((input) => {
          input.addEventListener("input", calculateTotals);
        });

        // Form submission enhancement
        const form = document.getElementById("salesForm");
        form.addEventListener("submit", function (e) {
          const submitBtn = form.querySelector('button[type="submit"]');
          submitBtn.innerHTML =
            '<span class="loading"></span> Recording Sale...';
          submitBtn.disabled = true;
        });

        // Initialize calculations
        calculateTotals();
      });
    </script>
  </body>
</html>
