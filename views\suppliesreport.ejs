<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/global-styles.css" />
    <script src="/javascript/suppliesreport.js" defer></script>
    <title>Supplies Report - Farm Management System</title>
  </head>
  <body>
    <!-- Modern Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="app-logo">Farm Management System</div>
        <div class="user-info">
          <span>Supplies Report</span>
        </div>
      </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav no-print">
      <div class="breadcrumb-content">
        <a href="/management/homepage" class="breadcrumb-item">Dashboard</a>
        <span class="breadcrumb-separator">›</span>
        <a href="/management/homepage" class="breadcrumb-item">Reports</a>
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-item active">Supplies Report</span>
      </div>
    </nav>

    <!-- Main Container -->
    <div class="app-container">
      <!-- Alerts -->
      <% if(hasErr){%>
      <div class="alert alert-danger">
        <div>
          <% errMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%> <% if(hasSuccess){%>
      <div class="alert alert-success">
        <div>
          <% successMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%>

      <!-- Page Header -->
      <div class="page-header no-print">
        <h1 class="page-title">📦 Supplies Report</h1>
        <p class="page-subtitle">
          Comprehensive supplies purchase analytics and inventory insights
        </p>
        <div class="page-actions">
          <a href="/management/homepage" class="btn btn-secondary btn-back"
            >Back to Dashboard</a
          >
          <a href="/record/supplies" class="btn btn-success btn-add"
            >Add New Supply Record</a
          >
          <button id="printBtn" class="btn btn-outline btn-print no-print">
            Print Report
          </button>
        </div>
      </div>

      <!-- Date Range Filter -->
      <div class="card mb-4 no-print">
        <div class="card-header">
          <h3 class="card-title">📅 Filter by Date Range</h3>
        </div>
        <div class="card-body">
          <form id="dateFilterForm" class="form-grid">
            <div class="form-group">
              <label for="from" class="form-label">📅 From Date</label>
              <input
                type="date"
                class="form-control"
                name="from"
                id="from"
                required
              />
            </div>
            <div class="form-group">
              <label for="to" class="form-label">📅 To Date</label>
              <input
                type="date"
                class="form-control"
                name="to"
                id="to"
                required
              />
            </div>
            <div class="form-group d-flex align-center">
              <button
                type="submit"
                class="btn btn-primary btn-search"
                id="submit"
              >
                🔍 Generate Report
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Report Data Table -->
      <div class="table-container" id="printableSection">
        <div class="table-header">
          <h3 class="table-title">
            Desire's Supplies Purchase Report For: <%= new
            Date().toLocaleDateString() %> <%= new Date().toLocaleTimeString()
            %>
          </h3>
          <div class="table-actions no-print">
            <span class="text-muted"
              ><%= Supplies.length %> supply records found</span
            >
          </div>
        </div>

        <table class="data-table" id="main-table">
          <thead>
            <tr>
              <th>Date</th>
              <th>Supplier</th>
              <th>Feed Type</th>
              <th>Feed Unit</th>
              <th>Stock Before</th>
              <th>Quantity</th>
              <th>Price per Unit</th>
              <th>Total Amount</th>
              <th>Stock After</th>
              <th>Staff Name</th>
              <th>Staff No</th>
              <th>Remarks</th>
            </tr>
          </thead>
          <tbody>
            <% if(Supplies && Supplies.length > 0) { %> <%
            Supplies.forEach((supply, index) => { %>
            <tr
              style="
                animation: fadeIn 0.3s ease-in-out <%= index * 0.1 %>s both;
              "
            >
              <td><%= new Date(supply.date).toLocaleDateString() %></td>
              <td><strong><%= supply.supplier %></strong></td>
              <td>
                <span class="status-indicator status-info">
                  <%= supply.feed_type %>
                </span>
              </td>
              <td><%= supply.feed_unit %></td>
              <td>
                <span class="text-muted"><%= supply.stock_bf %></span>
              </td>
              <td><strong><%= supply.quantity %></strong></td>
              <td>₦<%= Number(supply.price).toLocaleString() %></td>
              <td>
                <strong>₦<%= Number(supply.amount).toLocaleString() %></strong>
              </td>
              <td>
                <span class="text-success"><%= supply.stock_cf %></span>
              </td>
              <td><%= supply.staff_name %></td>
              <td><%= supply.staff_no %></td>
              <td><%= supply.remark || '-' %></td>
            </tr>
            <% }) %> <% } else { %>
            <tr>
              <td colspan="12" class="text-center p-4">
                <div class="text-muted">
                  <h5>📭 No Supply Records Found</h5>
                  <p>No supply records found for the selected date range.</p>
                  <a href="/record/supplies" class="btn btn-primary btn-sm">
                    ➕ Create First Supply Record
                  </a>
                </div>
              </td>
            </tr>
            <% } %>
          </tbody>
        </table>

        <!-- Summary Table -->
        <div id="display-tr" class="mt-4"></div>
      </div>
    </div>

    <!-- Enhanced JavaScript -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const fromInput = document.getElementById("from");
        const toInput = document.getElementById("to");

        // Set default date range (last 30 days)
        const today = new Date();
        const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

        toInput.value = today.toISOString().split("T")[0];
        fromInput.value = lastMonth.toISOString().split("T")[0];

        // Print button functionality
        const printBtn = document.getElementById("printBtn");
        if (printBtn) {
          printBtn.addEventListener("click", function () {
            window.print();
          });
        }
      });
    </script>

    <!-- Add CSS animations -->
    <style>
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .status-indicator {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-weight: 600;
      }

      .status-info {
        background: #e3f2fd;
        color: #1565c0;
      }
      .status-success {
        background: #e8f5e8;
        color: #2e7d32;
      }
      .status-warning {
        background: #fff3e0;
        color: #ef6c00;
      }
      .status-danger {
        background: #ffebee;
        color: #c62828;
      }
    </style>
  </body>
</html>
