const refreshBtn = document.getElementById("refreshBtn");
const dataTableBody = document.getElementById("data-table-body");
const printBtn = document.getElementById("printbtn");
const searchDiv = document.getElementById("searchdiv");

// Function to load customer data
function loadCustomerData() {
  fetch("/management/getallcustomer")
    .then((res) => res.json())
    .then((data) => {
      let htmlContent = "";
      if (data && data.length > 0) {
        data.forEach((record) => {
          const dateFormatted = record.createdAt ? new Date(record.createdAt).toLocaleDateString() : 'N/A';
          // Color code based on account balance
          let balanceColor = '';
          if (record.account_balance < 0) {
            balanceColor = ' style="background-color: #ffebee; color: #c62828;"'; // Red for negative balance
          } else if (record.account_balance > 100000) {
            balanceColor = ' style="background-color: #e8f5e8; color: #2e7d32;"'; // Green for high balance
          }
          
          htmlContent += `
            <tr${balanceColor}>
              <td>${record.customer_id}</td>
              <td>${record.name}</td>
              <td>${record.address}</td>
              <td>₦${record.account_balance.toLocaleString()}</td>
              <td>${record.account_no}</td>
              <td>${record.bank}</td>
              <td>${dateFormatted}</td>
            </tr>
          `;
        });
      } else {
        htmlContent = `
          <tr>
            <td colspan="7" style="text-align: center; padding: 20px;">
              No customer records found
            </td>
          </tr>
        `;
      }
      dataTableBody.innerHTML = htmlContent;
    })
    .catch((error) => {
      console.error("Error loading customer data:", error);
      dataTableBody.innerHTML = `
        <tr>
          <td colspan="7" style="text-align: center; padding: 20px; color: red;">
            Error loading data. Please try again.
          </td>
        </tr>
      `;
    });
}

// Load data when page loads
document.addEventListener("DOMContentLoaded", loadCustomerData);

// Refresh button event listener
refreshBtn.addEventListener("click", (e) => {
  e.preventDefault();
  loadCustomerData();
});

// Print button event listener
printBtn.addEventListener("click", (e) => {
  e.preventDefault();
  searchDiv.style.display = "none";
  printBtn.style.display = "none";
  window.print();
  // Restore elements after printing
  setTimeout(() => {
    searchDiv.style.display = "block";
    printBtn.style.display = "block";
  }, 1000);
});
