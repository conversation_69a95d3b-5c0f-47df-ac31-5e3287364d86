const fromInput = document.getElementById("from");
const toInput = document.getElementById("to");
const submitBtn = document.getElementById("submit");
const dateFilterForm = document.getElementById("dateFilterForm");
const recordContainer = document.getElementById("record-container");

// Listen for filter form submit
if (dateFilterForm) {
  dateFilterForm.addEventListener("submit", function (e) {
    e.preventDefault();
    const from = fromInput.value;
    const to = toInput.value;
    // Fetch filtered records from the server
    fetch(`/management/filter-birdpen/${from}/${to}`)
      .then((res) => res.json())
      .then((data) => {
        // Build filtered record rows
        let html = `
          <div class='record-header' id='record-header'>
            <p>Date Created</p>
            <p>Pen Code</p>
            <p>Pen Name</p>
            <p>Bird Type</p>
            <p>no of cage</p>
            <p>Cage Capacity</p>
            <p>Current Stock</p>
            <p>Batch</p>
            <span style='width:48px'></span>
          </div>
        `;
        if (data.length > 0) {
          data.forEach(function (record) {
            html += `
              <div class='record-row'>
                <p>${new Date(record.date).toLocaleDateString()}</p>
                <p>${record.code}</p>
                <p>${record.name}</p>
                <p>${record.type}</p>
                <p>${record.no_of_cage}</p>
                <p>${record.cage_capacity}</p>
                <p>${record.stock}</p>
                <p>${record.batch}</p>
                <a href="/deleterecord/birdpen/${
                  record._id
                }"><i class='material-icons' style='font-size:32px;color:red'>delete</i></a>
              </div>
            `;
          });
        } else {
          html += `<p style='text-align:center'>no record found</p>`;
        }
        recordContainer.innerHTML = html;
      })
      .catch((err) => {
        recordContainer.innerHTML = `<p style='text-align:center;color:red'>Error loading records</p>`;
      });
  });
}
