const express = require("express");
const router = express.Router();
const BirdIntake = require("../model/BirdIntake");
const deleteRecordControll = require("../controller/deleterecord");
const BirdPen = require("../model/BirdPen");
const Staff = require("../model/Staff");
const Supplier = require("../model/Supplier");
const Customer = require("../model/Customer");
const FeedType = require("../model/FeedType");
const FeedDetails = require("../model/FeedDetails");
const BirdRemoved = require("../model/BirdRemoved");
const DailyProductionRecord = require("../model/DailyProductionRecord");
const MedicationRecord = require("../model/MedicationRecord");
const SalesRecord = require("../model/SalesRecord");
const SuppliesRecord = require("../model/SuppliesRecord");

router.get("/", (req, res) => {
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("deleterecordscreen/deletemenuscreen", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
  });
});

router.get("/birdintake", async (req, res) => {
  const birdIntake = await BirdIntake.find();
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("deleterecordscreen/birdintakescreen", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
    hasBirdIntake: birdIntake.length > 0,
    birdIntake,
  });
});

router.get("/birdremove", async (req, res) => {
  const birdRemove = await BirdRemoved.find();
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("deleterecordscreen/birdremovescreen", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
    hasBirdRemove: birdRemove.length > 0,
    birdRemove,
  });
});

router.get("/birdpen", async (req, res) => {
  const birdPen = await BirdPen.find();
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("deleterecordscreen/birdpenscreen", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
    hasBirdPen: birdPen.length > 0,
    birdPen,
  });
});

router.get("/feedtype", async (req, res) => {
  const feedType = await FeedType.find();
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("deleterecordscreen/feedtypescreen", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
    hasFeedType: feedType.length > 0,
    feedType,
  });
});

router.get("/feeddetail", async (req, res) => {
  const feedDetails = await FeedDetails.find();
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("deleterecordscreen/feeddetailscreen", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
    hasFeedDetails: feedDetails.length > 0,
    feedDetails,
  });
});

router.get("/customer", async (req, res) => {
  const customer = await Customer.find();
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("deleterecordscreen/customerscreen", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
    hasCustomer: customer.length > 0,
    customer,
  });
});
router.get("/supplier", async (req, res) => {
  const supplier = await Supplier.find();
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("deleterecordscreen/supplierscreen", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
    hasSupplier: supplier.length > 0,
    supplier,
  });
});

router.get("/staff", async (req, res) => {
  const staff = await Staff.find();
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("deleterecordscreen/staffscreen", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
    hasStaff: staff.length > 0,
    staff,
  });
});

router.get("/dailyproductionrecord", async (req, res) => {
  const dailyProductionRecord = await DailyProductionRecord.find();
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("deleterecordscreen/dailyproductionrecordscreen", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
    hasDailyProductionRecord: dailyProductionRecord.length > 0,
    dailyProductionRecord,
  });
});
router.get("/salesrecord", async (req, res) => {
  const salesRecord = await SalesRecord.find();
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("deleterecordscreen/salesrecordscreen", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
    hasSalesRecord: salesRecord.length > 0,
    salesRecord,
  });
});
router.get("/suppliesrecord", async (req, res) => {
  const suppliesRecord = await SuppliesRecord.find();
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("deleterecordscreen/suppliesrecordscreen", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
    hasSuppliesRecord: suppliesRecord.length > 0,
    suppliesRecord,
  });
});
router.get("/medicationrecord", async (req, res) => {
  const medicationRecord = await MedicationRecord.find();
  let errMsg = req.flash("error");
  let successMsg = req.flash("success");
  return res.render("deleterecordscreen/medicationrecordscreen", {
    successMsg,
    errMsg,
    hasErr: errMsg.length > 0,
    hasSuccess: successMsg.length > 0,
    hasMedicationRecord: medicationRecord.length > 0,
    medicationRecord,
  });
});

router.get("/birdintake/:id", deleteRecordControll.deleteBirdIntake);
router.get("/birdpen/:id", deleteRecordControll.deleteBirdPen);
router.get("/birdremove/:id", deleteRecordControll.deleteBirdRemove);
router.get("/feedtype/:id", deleteRecordControll.deleteFeedType);
router.get("/feeddetails/:id", deleteRecordControll.deleteFeedDetails);
router.get("/customer/:id", deleteRecordControll.deleteCustomer);
router.get("/staff/:id", deleteRecordControll.deleteStaff);
router.get("/supplier/:id", deleteRecordControll.deleteSupplier);
router.get(
  "/dailyproductionrecord/:id",
  deleteRecordControll.deleteDailyProductionRecord
);
router.get(
  "/medicationrecord/:id",
  deleteRecordControll.deleteMedicationRecord
);
router.get("/salesrecord/:id", deleteRecordControll.deleteSalesRecord);
router.get("/suppliesrecord/:id", deleteRecordControll.deleteSuppliesRecord);

module.exports = router;
