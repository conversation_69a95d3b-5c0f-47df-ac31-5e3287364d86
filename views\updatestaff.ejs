<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/global-styles.css" />
    <title>Update Staff Account - Farm Management System</title>
  </head>
  <body>
    <!-- Modern Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="app-logo">Farm Management System</div>
        <div class="user-info">
          <span>Update Staff Account</span>
        </div>
      </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav">
      <div class="breadcrumb-content">
        <a href="/management/homepage" class="breadcrumb-item">Dashboard</a>
        <span class="breadcrumb-separator">›</span>
        <a href="/management/homepage" class="breadcrumb-item">Management</a>
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-item active">Update Staff</span>
      </div>
    </nav>

    <!-- Main Container -->
    <div class="app-container">
      <!-- Alerts -->
      <% if(hasErr){%>
      <div class="alert alert-danger">
        <div>
          <% errMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%> <% if(hasSuccess){%>
      <div class="alert alert-success">
        <div>
          <% successMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%>

      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">✏️ Update Staff Account</h1>
        <p class="page-subtitle">Search and update staff member information</p>
        <div class="page-actions">
          <a href="/management/homepage" class="btn btn-secondary btn-back"
            >Back to Dashboard</a
          >
          <a href="/management/check-staff" class="btn btn-outline"
            >View All Staff</a
          >
          <a href="/management/createstaff" class="btn btn-success"
            >Create New Staff</a
          >
        </div>
      </div>

      <!-- STEP 1: Search Staff Section -->
      <div class="card mb-4" id="searchSection">
        <div class="card-header">
          <h3 class="card-title">🔍 Step 1: Search Staff Member</h3>
          <p class="card-subtitle">
            Enter the staff name to find and update their information
          </p>
        </div>
        <div class="card-body">
          <div class="search-container">
            <div class="form-group">
              <label for="searchName" class="form-label">👤 Staff Name</label>
              <div class="search-input-group">
                <input
                  type="text"
                  class="form-control"
                  id="searchName"
                  placeholder="Enter staff full name to search..."
                  autocomplete="off"
                />
                <button type="button" class="btn btn-primary" id="searchBtn">
                  🔍 Search Staff
                </button>
              </div>
              <small class="text-muted"
                >Enter the exact name as registered in the system</small
              >
            </div>

            <!-- Search Results -->
            <div id="searchResults" class="search-results d-none">
              <div class="alert alert-info">
                <div id="searchResultContent"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- STEP 2: Update Staff Form (Initially Hidden) -->
      <div class="card mb-4 d-none" id="updateSection">
        <div class="card-header">
          <h3 class="card-title">✏️ Step 2: Update Staff Information</h3>
          <p class="card-subtitle">Modify the staff member's details below</p>
        </div>
        <div class="card-body">
          <form
            action="/management/updatestaff"
            method="POST"
            id="updateStaffForm"
          >
            <!-- Current Staff Info Display -->
            <div class="alert alert-success mb-4" id="currentStaffInfo">
              <h5>📋 Current Staff Information:</h5>
              <div id="currentStaffDetails"></div>
            </div>

            <!-- Staff Identification -->
            <div class="card mb-4">
              <div class="card-header">
                <h4 class="card-title">🆔 Staff Identification</h4>
              </div>
              <div class="card-body">
                <div class="form-grid">
                  <div class="form-group">
                    <label for="staff_no" class="form-label"
                      >🆔 Staff Number</label
                    >
                    <input
                      type="text"
                      class="form-control"
                      name="staff_no"
                      id="staff_no"
                      placeholder="Enter staff number"
                      required
                      readonly
                    />
                    <small class="text-muted"
                      >Staff number cannot be changed</small
                    >
                  </div>

                  <div class="form-group">
                    <label for="level" class="form-label">👔 Staff Level</label>
                    <select
                      name="level"
                      class="form-control form-select"
                      id="level"
                      required
                    >
                      <option value="">Select staff level...</option>
                      <option value="attendant">👷 Attendant</option>
                      <option value="supervisor">👨‍💼 Supervisor</option>
                      <option value="manager">🧑‍💼 Manager</option>
                      <option value="director">👑 Director</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            <!-- Personal Information -->
            <div class="card mb-4">
              <div class="card-header">
                <h4 class="card-title">👤 Personal Information</h4>
              </div>
              <div class="card-body">
                <div class="form-grid">
                  <div class="form-group">
                    <label for="name" class="form-label">👤 Full Name</label>
                    <input
                      type="text"
                      class="form-control"
                      name="name"
                      id="name"
                      placeholder="Enter staff full name"
                      required
                      readonly
                    />
                    <small class="text-muted"
                      >Name cannot be changed (used for identification)</small
                    >
                  </div>

                  <div class="form-group" style="grid-column: 1 / -1">
                    <label for="address" class="form-label"
                      >📍 Home Address</label
                    >
                    <textarea
                      class="form-control"
                      name="address"
                      id="address"
                      placeholder="Enter staff complete address"
                      rows="3"
                      required
                    ></textarea>
                  </div>

                  <div class="form-group">
                    <label for="guarantor" class="form-label"
                      >🤝 Guarantor Name</label
                    >
                    <input
                      type="text"
                      class="form-control"
                      name="guarantor"
                      id="guarantor"
                      placeholder="Enter guarantor's full name"
                      required
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- Financial Information -->
            <div class="card mb-4">
              <div class="card-header">
                <h4 class="card-title">💰 Financial Information</h4>
              </div>
              <div class="card-body">
                <div class="form-grid">
                  <div class="form-group">
                    <label for="account_no" class="form-label"
                      >🏦 Account Number</label
                    >
                    <input
                      type="text"
                      class="form-control"
                      name="account_no"
                      id="account_no"
                      placeholder="Enter bank account number"
                      required
                    />
                  </div>

                  <div class="form-group">
                    <label for="bank" class="form-label">🏛️ Bank Name</label>
                    <input
                      type="text"
                      class="form-control"
                      name="bank"
                      id="bank"
                      placeholder="Enter bank name"
                      required
                    />
                  </div>

                  <div class="form-group">
                    <label for="expected_salary" class="form-label"
                      >💰 Expected Salary</label
                    >
                    <input
                      type="text"
                      class="form-control"
                      name="Expected_salary"
                      id="expected_salary"
                      placeholder="Enter expected salary"
                      required
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- Update Summary -->
            <div class="card mb-4">
              <div class="card-header">
                <h4 class="card-title">📊 Update Summary</h4>
              </div>
              <div class="card-body">
                <div class="grid grid-4">
                  <div class="text-center">
                    <h5 class="text-primary" id="summaryStaffNo">-</h5>
                    <p class="text-muted">Staff Number</p>
                  </div>
                  <div class="text-center">
                    <h5 class="text-info" id="summaryName">-</h5>
                    <p class="text-muted">Staff Name</p>
                  </div>
                  <div class="text-center">
                    <h5 class="text-warning" id="summaryLevel">-</h5>
                    <p class="text-muted">Staff Level</p>
                  </div>
                  <div class="text-center">
                    <h5 class="text-success" id="summaryBank">-</h5>
                    <p class="text-muted">Bank</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="d-flex justify-between align-center">
              <div>
                <button
                  type="button"
                  class="btn btn-secondary"
                  id="backToSearchBtn"
                >
                  ← Back to Search
                </button>
              </div>
              <div class="d-flex gap-2">
                <button type="reset" class="btn btn-outline">
                  🔄 Reset Changes
                </button>
                <button type="submit" class="btn btn-success btn-save">
                  ✏️ Update Staff
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Enhanced JavaScript -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const searchSection = document.getElementById("searchSection");
        const updateSection = document.getElementById("updateSection");
        const searchBtn = document.getElementById("searchBtn");
        const searchName = document.getElementById("searchName");
        const searchResults = document.getElementById("searchResults");
        const searchResultContent = document.getElementById(
          "searchResultContent"
        );
        const backToSearchBtn = document.getElementById("backToSearchBtn");
        const updateForm = document.getElementById("updateStaffForm");

        let foundStaff = null;

        // Search functionality
        searchBtn.addEventListener("click", function () {
          const name = searchName.value.trim();
          if (!name) {
            alert("Please enter a staff name to search");
            return;
          }

          // Show loading
          searchBtn.innerHTML = '<span class="loading"></span> Searching...';
          searchBtn.disabled = true;

          // Real API call to search staff by name
          fetch(`/management/searchstaff/${encodeURIComponent(name)}`)
            .then((response) => response.json())
            .then((data) => {
              if (data.found) {
                foundStaff = data.staff;
                showSearchResult(true, data.staff);
              } else {
                foundStaff = null;
                showSearchResult(false, null);
              }
            })
            .catch((error) => {
              console.error("Error searching for staff:", error);
              foundStaff = null;
              showSearchResult(false, null);
            })
            .finally(() => {
              // Reset button
              searchBtn.innerHTML = "🔍 Search Staff";
              searchBtn.disabled = false;
            });
        });

        function showSearchResult(found, staff) {
          searchResults.classList.remove("d-none");

          if (found) {
            searchResultContent.innerHTML = `
              <div class="d-flex justify-between align-center">
                <div>
                  <h5 class="text-success mb-1">✅ Staff Found!</h5>
                  <p class="mb-0"><strong>Name:</strong> ${staff.name}</p>
                  <p class="mb-0"><strong>Staff No:</strong> ${staff.staff_no}</p>
                  <p class="mb-0"><strong>Level:</strong> ${staff.level}</p>
                </div>
                <button type="button" class="btn btn-success" id="proceedToUpdateBtn">
                  Proceed to Update →
                </button>
              </div>
            `;

            // Add proceed button functionality
            document
              .getElementById("proceedToUpdateBtn")
              .addEventListener("click", function () {
                showUpdateForm(staff);
              });
          } else {
            searchResultContent.innerHTML = `
              <div class="text-center">
                <h5 class="text-danger mb-1">❌ Staff Not Found</h5>
                <p class="mb-0">No staff member found with the name "${searchName.value}"</p>
                <p class="mb-0 text-muted">Please check the spelling and try again</p>
              </div>
            `;
          }
        }

        function showUpdateForm(staff) {
          // Hide search section and show update section
          searchSection.classList.add("d-none");
          updateSection.classList.remove("d-none");

          // Populate current staff info
          document.getElementById("currentStaffDetails").innerHTML = `
            <div class="row">
              <div class="col-md-6">
                <p><strong>Staff Number:</strong> ${staff.staff_no}</p>
                <p><strong>Name:</strong> ${staff.name}</p>
                <p><strong>Level:</strong> ${staff.level}</p>
              </div>
              <div class="col-md-6">
                <p><strong>Address:</strong> ${staff.address}</p>
                <p><strong>Bank:</strong> ${staff.bank}</p>
                <p><strong>Guarantor:</strong> ${staff.guarantor}</p>
              </div>
            </div>
          `;

          // Populate form fields
          document.getElementById("staff_no").value = staff.staff_no;
          document.getElementById("name").value = staff.name;
          document.getElementById("level").value = staff.level;
          document.getElementById("address").value = staff.address;
          document.getElementById("account_no").value = staff.account_no;
          document.getElementById("bank").value = staff.bank;
          document.getElementById("guarantor").value = staff.guarantor;
          document.getElementById("expected_salary").value =
            staff.expected_salary;

          // Update summary
          updateSummary();
        }

        // Back to search functionality
        backToSearchBtn.addEventListener("click", function () {
          updateSection.classList.add("d-none");
          searchSection.classList.remove("d-none");
          searchResults.classList.add("d-none");
          searchName.value = "";
          updateForm.reset();
        });

        // Update summary function
        function updateSummary() {
          const staffNo = document.getElementById("staff_no").value || "-";
          const name = document.getElementById("name").value || "-";
          const level =
            document.getElementById("level").options[
              document.getElementById("level").selectedIndex
            ]?.text || "-";
          const bank = document.getElementById("bank").value || "-";

          document.getElementById("summaryStaffNo").textContent = staffNo;
          document.getElementById("summaryName").textContent = name;
          document.getElementById("summaryLevel").textContent =
            level.split(" ")[1] || level;
          document.getElementById("summaryBank").textContent = bank;
        }

        // Add event listeners for real-time updates
        ["level", "bank"].forEach((id) => {
          document.getElementById(id).addEventListener("change", updateSummary);
          document.getElementById(id).addEventListener("input", updateSummary);
        });

        // Form submission enhancement
        updateForm.addEventListener("submit", function (e) {
          const submitBtn = updateForm.querySelector('button[type="submit"]');
          submitBtn.innerHTML = '<span class="loading"></span> Updating...';
          submitBtn.disabled = true;
        });

        // Enter key search
        searchName.addEventListener("keypress", function (e) {
          if (e.key === "Enter") {
            searchBtn.click();
          }
        });
      });
    </script>
  </body>
</html>
