<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/global-styles.css" />
    <title>Supplies Record - Farm Management System</title>
  </head>
  <body>
    <!-- Modern Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="app-logo">Farm Management System</div>
        <div class="user-info">
          <span>Supplies Record</span>
        </div>
      </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav">
      <div class="breadcrumb-content">
        <a href="/management/homepage" class="breadcrumb-item">Dashboard</a>
        <span class="breadcrumb-separator">›</span>
        <a href="/management/homepage" class="breadcrumb-item">Records</a>
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-item active">Supplies Record</span>
      </div>
    </nav>

    <!-- Main Container -->
    <div class="app-container">
      <!-- Alerts -->
      <% if(hasErr){%>
      <div class="alert alert-danger">
        <div>
          <% errMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%> <% if(hasSuccess){%>
      <div class="alert alert-success">
        <div>
          <% successMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%>

      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">📦 Supplies Record</h1>
        <p class="page-subtitle">
          Record feed and supply purchases from suppliers
        </p>
        <div class="page-actions">
          <a href="/management/homepage" class="btn btn-secondary btn-back"
            >Back to Dashboard</a
          >
          <a href="/report/supplies" class="btn btn-outline"
            >View Supply Reports</a
          >
        </div>
      </div>
      <!-- Supplies Record Form -->
      <div class="form-container">
        <form action="/record/supplies" method="POST" id="suppliesForm">
          <!-- Supplier Information -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">🏭 Supplier Information</h3>
            </div>
            <div class="card-body">
              <div class="form-group">
                <label for="supplier" class="form-label"
                  >🚚 Select Supplier</label
                >
                <select
                  name="supplier"
                  class="form-control form-select"
                  id="supplier"
                  required
                >
                  <option value="">Choose supplier...</option>
                  <option value="CHI">CHI - Chi Farms Limited</option>
                  <option value="OBA">OBA - Oba Poultry Farms</option>
                  <option value="ANC">ANC - Anchor Farms Nigeria</option>
                  <option value="FSI">FSI - Farm Solutions Inc</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Supply Details -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">📦 Supply Details</h3>
            </div>
            <div class="card-body">
              <div class="form-grid">
                <div class="form-group">
                  <label for="feedtype" class="form-label">🌾 Feed Type</label>
                  <select
                    name="feedtype"
                    class="form-control form-select"
                    id="feedtype"
                    required
                  >
                    <option value="">Select feed type...</option>
                    <% feedType.forEach(type =>{%>
                    <option value="<%= type%>"><%= type%></option>
                    <% })%>
                  </select>
                </div>

                <div class="form-group">
                  <label for="feed_unit" class="form-label"
                    >📏 Unit of Measurement</label
                  >
                  <select
                    name="feed_unit"
                    class="form-control form-select"
                    id="feed_unit"
                    required
                  >
                    <option value="">Select unit...</option>
                    <option value="bag">Bag</option>
                    <option value="boxes">Boxes</option>
                    <option value="bottles">Bottles</option>
                    <option value="pieces">Pieces</option>
                    <option value="sachets">Sachets</option>
                  </select>
                </div>

                <div class="form-group">
                  <label for="quantity" class="form-label">🔢 Quantity</label>
                  <input
                    type="number"
                    class="form-control"
                    name="quantity"
                    id="quantity"
                    placeholder="Enter quantity received"
                    min="1"
                    step="0.1"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="price" class="form-label"
                    >💰 Price per Unit (₦)</label
                  >
                  <input
                    type="number"
                    class="form-control"
                    name="price"
                    id="price"
                    placeholder="Enter price per unit"
                    min="0"
                    step="0.01"
                    required
                  />
                </div>

                <div class="form-group" style="grid-column: 1 / -1">
                  <label for="remark" class="form-label">📝 Remarks</label>
                  <textarea
                    class="form-control"
                    name="remark"
                    id="remark"
                    placeholder="Enter delivery notes, quality observations, or other remarks"
                    rows="3"
                  ></textarea>
                </div>
              </div>
            </div>
          </div>

          <!-- Purchase Summary -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">📊 Purchase Summary</h3>
            </div>
            <div class="card-body">
              <div class="grid grid-4">
                <div class="text-center">
                  <h4 class="text-primary" id="totalQuantity">0</h4>
                  <p class="text-muted">Total Quantity</p>
                </div>
                <div class="text-center">
                  <h4 class="text-info" id="unitPrice">₦0.00</h4>
                  <p class="text-muted">Price per Unit</p>
                </div>
                <div class="text-center">
                  <h4 class="text-success" id="totalAmount">₦0.00</h4>
                  <p class="text-muted">Total Amount</p>
                </div>
                <div class="text-center">
                  <h4 class="text-warning" id="selectedSupplier">-</h4>
                  <p class="text-muted">Supplier</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="d-flex justify-between align-center">
            <div>
              <span class="text-muted"
                >All fields are required except remarks</span
              >
            </div>
            <div class="d-flex gap-2">
              <button type="reset" class="btn btn-secondary">
                🔄 Reset Form
              </button>
              <button type="submit" class="btn btn-success btn-save">
                📦 Record Supply
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Enhanced JavaScript -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const supplierSelect = document.getElementById("supplier");
        const quantityInput = document.getElementById("quantity");
        const priceInput = document.getElementById("price");
        const unitSelect = document.getElementById("feed_unit");

        const totalQuantityDisplay = document.getElementById("totalQuantity");
        const unitPriceDisplay = document.getElementById("unitPrice");
        const totalAmountDisplay = document.getElementById("totalAmount");
        const selectedSupplierDisplay =
          document.getElementById("selectedSupplier");

        function updateDisplays() {
          const supplier =
            supplierSelect.options[supplierSelect.selectedIndex]?.text || "-";
          const quantity = parseFloat(quantityInput.value) || 0;
          const price = parseFloat(priceInput.value) || 0;
          const unit = unitSelect.value || "";
          const totalAmount = quantity * price;

          totalQuantityDisplay.textContent =
            quantity.toLocaleString() + (unit ? " " + unit : "");
          unitPriceDisplay.textContent = new Intl.NumberFormat("en-NG", {
            style: "currency",
            currency: "NGN",
          }).format(price);
          totalAmountDisplay.textContent = new Intl.NumberFormat("en-NG", {
            style: "currency",
            currency: "NGN",
          }).format(totalAmount);
          selectedSupplierDisplay.textContent = supplier.split(" - ")[0] || "-";
        }

        // Add event listeners
        [supplierSelect, quantityInput, priceInput, unitSelect].forEach(
          (input) => {
            input.addEventListener("input", updateDisplays);
            input.addEventListener("change", updateDisplays);
          }
        );

        // Form submission enhancement
        const form = document.getElementById("suppliesForm");
        form.addEventListener("submit", function (e) {
          const submitBtn = form.querySelector('button[type="submit"]');
          submitBtn.innerHTML = '<span class="loading"></span> Recording...';
          submitBtn.disabled = true;
        });

        // Initialize displays
        updateDisplays();
      });
    </script>
  </body>
</html>
