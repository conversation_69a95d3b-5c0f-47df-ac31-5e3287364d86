const customerBtn = document.getElementById("customerbtn");
const supplierBtn = document.getElementById("supplierbtn");
const staffBtn = document.getElementById("staffbtn");
const feedTypeBtn = document.getElementById("feedtypebtn");
const feedDetailsBtn = document.getElementById("feeddetailsbtn");
const birdInTakeBtn = document.getElementById("birdintakebtn");
const dailyProductionBtn = document.getElementById("dailyproductionbtn");
const medicationBtn = document.getElementById("medicationbtn");
const salesBtn = document.getElementById("salesbtn");
const suppliesBtn = document.getElementById("suppliesbtn");
const displayScreen = document.getElementById("display-result");

customerBtn.addEventListener("click", (e) => {
  e.preventDefault();
  displayScreen.innerHTML = "";
  fetch("/management/getallcustomer")
    .then((res) => res.json())
    .then((data) => {
      let htmlContent = "";
      data.forEach((item) => {
        htmlContent += '<div class="items">';
        for (const key in item) {
          if (item.hasOwnProperty(key)) {
            htmlContent += `<h6><strong>${key}:</strong> ${JSON.stringify(
              item[key]
            )}</h6>`;
          }
        }
        htmlContent += "</div>";
      });
      displayScreen.innerHTML = htmlContent;
    })
    .catch((err) => {
      console.log("error:", err);
      displayScreen.innerHTML = "error fetching data or no data found";
    });
});

supplierBtn.addEventListener("click", (e) => {
  e.preventDefault();
  displayScreen.innerHTML = "";
  fetch("/management/getallsupplier")
    .then((res) => res.json())
    .then((data) => {
      let htmlContent = "";
      data.forEach((item) => {
        htmlContent += '<div class="items">';
        for (const key in item) {
          if (item.hasOwnProperty(key)) {
            htmlContent += `<h6><strong>${key}:</strong> ${JSON.stringify(
              item[key]
            )}</h6>`;
          }
        }
        htmlContent += "</div>";
      });
      displayScreen.innerHTML = htmlContent;
    })
    .catch((err) => {
      console.log("error:", err);
      displayScreen.innerHTML = "error fetching data or no data found";
    });
});

staffBtn.addEventListener("click", (e) => {
  e.preventDefault();
  displayScreen.innerHTML = "";
  fetch("/management/getallstaff")
    .then((res) => res.json())
    .then((data) => {
      let htmlContent = "";
      data.forEach((item) => {
        htmlContent += '<div class="items">';
        for (const key in item) {
          if (item.hasOwnProperty(key)) {
            htmlContent += `<h6><strong>${key}:</strong> ${JSON.stringify(
              item[key]
            )}</h6>`;
          }
        }
        htmlContent += "</div>";
      });
      displayScreen.innerHTML = htmlContent;
    })
    .catch((err) => {
      console.log("error:", err);
      displayScreen.innerHTML = "error fetching data or no data found";
    });
});

birdInTakeBtn.addEventListener("click", (e) => {
  e.preventDefault();
  displayScreen.innerHTML = "";
  fetch("/management/allbirdintake")
    .then((res) => res.json())
    .then((data) => {
      let htmlContent = "";
      data.forEach((item) => {
        htmlContent += '<div class="items">';
        for (const key in item) {
          if (item.hasOwnProperty(key)) {
            htmlContent += `<h6><strong>${key}:</strong> ${JSON.stringify(
              item[key]
            )}</h6>`;
          }
        }
        htmlContent += "</div>";
      });
      displayScreen.innerHTML = htmlContent;
    })
    .catch((err) => {
      console.log("error:", err);
      displayScreen.innerHTML = "error fetching data or no data found";
    });
});

feedTypeBtn.addEventListener("click", (e) => {
  e.preventDefault();
  displayScreen.innerHTML = "";
  fetch("/management/allfeedtype")
    .then((res) => res.json())
    .then((data) => {
      let htmlContent = "";
      data.forEach((item) => {
        htmlContent += '<div class="items">';
        for (const key in item) {
          if (item.hasOwnProperty(key)) {
            htmlContent += `<h6><strong>${key}:</strong> ${JSON.stringify(
              item[key]
            )}</h6>`;
          }
        }
        htmlContent += "</div>";
      });
      displayScreen.innerHTML = htmlContent;
    })
    .catch((err) => {
      console.log("error:", err);
      displayScreen.innerHTML = "error fetching data or no data found";
    });
});

feedDetailsBtn.addEventListener("click", (e) => {
  e.preventDefault();
  displayScreen.innerHTML = "";
  fetch("/management/allfeeddetails")
    .then((res) => res.json())
    .then((data) => {
      let htmlContent = "";
      data.forEach((item) => {
        htmlContent += '<div class="items">';
        for (const key in item) {
          if (item.hasOwnProperty(key)) {
            htmlContent += `<h6><strong>${key}:</strong> ${JSON.stringify(
              item[key]
            )}</h6>`;
          }
        }
        htmlContent += "</div>";
      });
      displayScreen.innerHTML = htmlContent;
    })
    .catch((err) => {
      console.log("error:", err);
      displayScreen.innerHTML = "error fetching data or no data found";
    });
});

dailyProductionBtn.addEventListener("click", (e) => {
  e.preventDefault();
  displayScreen.innerHTML = "";
  fetch("/record/allproductionrecord")
    .then((res) => res.json())
    .then((data) => {
      let htmlContent = "";
      data.forEach((item) => {
        htmlContent += '<div class="items">';
        for (const key in item) {
          if (item.hasOwnProperty(key)) {
            htmlContent += `<h6><strong>${key}:</strong> ${JSON.stringify(
              item[key]
            )}</h6>`;
          }
        }
        htmlContent += "</div>";
      });
      displayScreen.innerHTML = htmlContent;
    })
    .catch((err) => {
      console.log("error:", err);
      displayScreen.innerHTML = "error fetching data or no data found";
    });
});

medicationBtn.addEventListener("click", (e) => {
  e.preventDefault();
  displayScreen.innerHTML = "";
  fetch("/record/allmedicationrecord")
    .then((res) => res.json())
    .then((data) => {
      let htmlContent = "";
      data.forEach((item) => {
        htmlContent += '<div class="items">';
        for (const key in item) {
          if (item.hasOwnProperty(key)) {
            htmlContent += `<h6><strong>${key}:</strong> ${JSON.stringify(
              item[key]
            )}</h6>`;
          }
        }
        htmlContent += "</div>";
      });
      displayScreen.innerHTML = htmlContent;
    })
    .catch((err) => {
      console.log("error:", err);
      displayScreen.innerHTML = "error fetching data or no data found";
    });
});

salesBtn.addEventListener("click", (e) => {
  e.preventDefault();
  displayScreen.innerHTML = "";
  fetch("/record/allsalesrecord")
    .then((res) => res.json())
    .then((data) => {
      let htmlContent = "";
      data.forEach((item) => {
        htmlContent += '<div class="items">';
        for (const key in item) {
          if (item.hasOwnProperty(key)) {
            htmlContent += `<h6><strong>${key}:</strong> ${JSON.stringify(
              item[key]
            )}</h6>`;
          }
        }
        htmlContent += "</div>";
      });
      displayScreen.innerHTML = htmlContent;
    })
    .catch((err) => {
      console.log("error:", err);
      displayScreen.innerHTML = "error fetching data or no data found";
    });
});

suppliesBtn.addEventListener("click", (e) => {
  e.preventDefault();
  displayScreen.innerHTML = "";
  fetch("/record/allsuppliesrecord")
    .then((res) => res.json())
    .then((data) => {
      let htmlContent = "";
      data.forEach((item) => {
        htmlContent += '<div class="items">';
        for (const key in item) {
          if (item.hasOwnProperty(key)) {
            htmlContent += `<h6><strong>${key}:</strong> ${JSON.stringify(
              item[key]
            )}</h6>`;
          }
        }
        htmlContent += "</div>";
      });
      displayScreen.innerHTML = htmlContent;
    })
    .catch((err) => {
      console.log("error:", err);
      displayScreen.innerHTML = "error fetching data or no data found";
    });
});
