const fromInput = document.getElementById("from");
const toInput = document.getElementById("to");
const submitBtn = document.getElementById("submit");
const dateFilterForm = document.getElementById("dateFilterForm");
const recordContainer = document.getElementById("record-container");

// Listen for filter form submit
if (dateFilterForm) {
  dateFilterForm.addEventListener("submit", function (e) {
    e.preventDefault();
    const from = fromInput.value;
    const to = toInput.value;
    // Fetch filtered records from the server
    fetch(`/management/filter-customer/${from}/${to}`)
      .then((res) => res.json())
      .then((data) => {
        // Build filtered record rows
        let html = `
          <div class='record-header' id='record-header'>
                <p>Created Date</p>
                <p>Customer ID</p>
                <p>Name</p>
                <p>Address</p>
                <p>Account Balance</p>
                <p>Account Number</p>
                <p>Bank</p>
                <span style="width: 48px"></span>
          </div>
        `;
        if (data.length > 0) {
          data.forEach(function (record) {
            html += `
              <div class='record-row'>
                <p>${new Date(record.createdAt).toLocaleDateString()}</p>
                <p>${record.customer_id}</p>                 
                <p>${record.name}</p>                 
                <p>${record.address}</p>                 
                <p>${record.account_balance}</p>                 
                <p>${record.account_no}</p>                 
                <p>${record.bank}</p>                 
                 <a href="/deleterecord/customer/${
                   record._id
                 }"><i class="material-icons" style="font-size: 32px; color: red"
                  >delete</i></a>                 
              </div>                 
            `;
          });
        } else {
          html += `<p style='text-align:center'>no record found</p>`;
        }
        recordContainer.innerHTML = html;
      })
      .catch((err) => {
        recordContainer.innerHTML = `<p style='text-align:center;color:red'>Error loading records</p>`;
      });
  });
}
