<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/global-styles.css" />
    <script src="/javascript/check-customers.js" defer></script>
    <title>Customer Management - Director Only</title>
  </head>
  <body>
    <!-- Modern Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="app-logo">Farm Management System</div>
        <div class="user-info">
          <span>👑 Director - Customer Management</span>
        </div>
      </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav no-print">
      <div class="breadcrumb-content">
        <a href="/management/homepage" class="breadcrumb-item">Dashboard</a>
        <span class="breadcrumb-separator">›</span>
        <a href="/management/homepage" class="breadcrumb-item">Management</a>
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-item active">Customer Records</span>
      </div>
    </nav>

    <!-- Main Container -->
    <div class="app-container">
      <!-- Alerts -->
      <% if(hasErr){%>
      <div class="alert alert-danger">
        <div>
          <% errMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%> <% if(hasSuccess){%>
      <div class="alert alert-success">
        <div>
          <% successMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%>

      <!-- Page Header -->
      <div
        class="page-header no-print"
        style="border-left-color: var(--success-color)"
      >
        <h1 class="page-title">👥 Customer Management</h1>
        <p class="page-subtitle">
          Director-only access to customer accounts and financial data
        </p>
        <div class="page-actions">
          <a href="/management/homepage" class="btn btn-secondary btn-back"
            >Back to Dashboard</a
          >
          <a href="/management/createcustomer" class="btn btn-success btn-add"
            >Add New Customer</a
          >
          <a href="/management/updatecustomer" class="btn btn-warning btn-edit"
            >Update Customer</a
          >
          <button id="refreshBtn" class="btn btn-primary btn-refresh">
            Refresh Data
          </button>
        </div>
      </div>

      <!-- Data Table Container -->
      <div class="table-container">
        <div
          class="table-header no-print"
          style="
            background: linear-gradient(135deg, var(--success-color), #229954);
          "
        >
          <h3 class="table-title">Customer Database</h3>
          <div class="table-actions">
            <div id="loading-indicator" class="d-none">
              <span class="loading"></span> Loading...
            </div>
            <span id="record-count" class="text-muted">Loading records...</span>
          </div>
        </div>

        <table class="data-table" id="main-table">
          <thead>
            <tr>
              <th>Customer ID</th>
              <th>Name</th>
              <th>Address</th>
              <th>Account Balance</th>
              <th>Account Number</th>
              <th>Bank</th>
              <th>Created Date</th>
            </tr>
          </thead>
          <tbody id="data-table-body">
            <tr>
              <td colspan="7" class="text-center p-4">
                <div class="loading"></div>
                <span class="ml-2">Loading customer data...</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </body>
</html>
