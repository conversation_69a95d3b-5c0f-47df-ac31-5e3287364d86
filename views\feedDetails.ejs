<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/global-styles.css" />
    <title>Feed Details Management - Farm Management System</title>
  </head>
  <body>
    <!-- Modern Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="app-logo">Farm Management System</div>
        <div class="user-info">
          <span>Feed Details Management</span>
        </div>
      </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav">
      <div class="breadcrumb-content">
        <a href="/management/homepage" class="breadcrumb-item">Dashboard</a>
        <span class="breadcrumb-separator">›</span>
        <a href="/management/homepage" class="breadcrumb-item">Management</a>
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-item active">Feed Details</span>
      </div>
    </nav>

    <!-- Main Container -->
    <div class="app-container">
      <!-- Alerts -->
      <% if(hasErr){%>
      <div class="alert alert-danger">
        <div>
          <% errMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%> <% if(hasSuccess){%>
      <div class="alert alert-success">
        <div>
          <% successMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%>

      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">📦 Feed Details Management</h1>
        <p class="page-subtitle">
          Create and update feed inventory details and pricing
        </p>
        <div class="page-actions">
          <a href="/management/homepage" class="btn btn-secondary btn-back"
            >Back to Dashboard</a
          >
          <a href="/management/check-feeddetails" class="btn btn-outline"
            >View Feed Inventory</a
          >
        </div>
      </div>
      <!-- Feed Details Form -->
      <div class="form-container">
        <form
          action="/management/feeddetails"
          method="POST"
          id="feedDetailsForm"
        >
          <!-- Feed Selection -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">🏷️ Feed Selection</h3>
            </div>
            <div class="card-body">
              <div class="form-group">
                <label for="code" class="form-label">🌾 Feed Code</label>
                <select
                  name="code"
                  class="form-control form-select"
                  id="code"
                  required
                >
                  <option value="">Select feed type...</option>
                  <% feedcode.forEach(code =>{ %>
                  <option value="<%= code %>"><%= code %></option>
                  <%}) %>
                </select>
              </div>
            </div>
          </div>

          <!-- Inventory Details -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">📦 Inventory Details</h3>
            </div>
            <div class="card-body">
              <div class="form-grid">
                <div class="form-group">
                  <label for="stock" class="form-label">📊 Current Stock</label>
                  <input
                    type="number"
                    class="form-control"
                    name="stock"
                    id="stock"
                    placeholder="Enter current stock quantity"
                    min="0"
                    step="0.1"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="reorder_l" class="form-label"
                    >⚠️ Reorder Level</label
                  >
                  <input
                    type="number"
                    class="form-control"
                    name="reorder_l"
                    id="reorder_l"
                    placeholder="Enter minimum stock level"
                    min="0"
                    step="0.1"
                    required
                  />
                  <small class="text-muted"
                    >Alert when stock falls below this level</small
                  >
                </div>

                <div class="form-group">
                  <label for="unit" class="form-label"
                    >📏 Unit of Measurement</label
                  >
                  <select
                    name="unit"
                    class="form-control form-select"
                    id="unit"
                    required
                  >
                    <option value="">Select unit...</option>
                    <option value="bag">Bag</option>
                    <option value="boxes">Boxes</option>
                    <option value="bottles">Bottles</option>
                    <option value="pieces">Pieces</option>
                    <option value="sachets">Sachets</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          <!-- Supplier & Pricing -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">🏭 Supplier & Pricing</h3>
            </div>
            <div class="card-body">
              <div class="form-grid">
                <div class="form-group">
                  <label for="supplier" class="form-label">🚚 Supplier</label>
                  <select
                    name="supplier"
                    class="form-control form-select"
                    id="supplier"
                    required
                  >
                    <option value="">Select supplier...</option>
                    <option value="CHI">CHI - Chi Farms Limited</option>
                    <option value="OBA">OBA - Oba Poultry Farms</option>
                    <option value="ANC">ANC - Anchor Farms Nigeria</option>
                    <option value="FSI">FSI - Farm Solutions Inc</option>
                  </select>
                </div>

                <div class="form-group">
                  <label for="price" class="form-label"
                    >💰 Price per Unit (₦)</label
                  >
                  <input
                    type="number"
                    class="form-control"
                    name="price"
                    id="price"
                    placeholder="Enter price per unit"
                    min="0"
                    step="0.01"
                    required
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Inventory Summary -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">📊 Inventory Summary</h3>
            </div>
            <div class="card-body">
              <div class="grid grid-3">
                <div class="text-center">
                  <h4 class="text-primary" id="totalStock">0</h4>
                  <p class="text-muted">Current Stock</p>
                </div>
                <div class="text-center">
                  <h4 class="text-warning" id="reorderLevel">0</h4>
                  <p class="text-muted">Reorder Level</p>
                </div>
                <div class="text-center">
                  <h4 class="text-success" id="totalValue">₦0.00</h4>
                  <p class="text-muted">Total Value</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="d-flex justify-between align-center">
            <div>
              <span class="text-muted">All fields are required</span>
            </div>
            <div class="d-flex gap-2">
              <button type="reset" class="btn btn-secondary">
                🔄 Reset Form
              </button>
              <button type="submit" class="btn btn-success btn-save">
                📦 Update Feed Details
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Enhanced JavaScript -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const stockInput = document.getElementById("stock");
        const reorderInput = document.getElementById("reorder_l");
        const priceInput = document.getElementById("price");
        const unitSelect = document.getElementById("unit");

        const totalStockDisplay = document.getElementById("totalStock");
        const reorderLevelDisplay = document.getElementById("reorderLevel");
        const totalValueDisplay = document.getElementById("totalValue");

        function updateDisplays() {
          const stock = parseFloat(stockInput.value) || 0;
          const reorder = parseFloat(reorderInput.value) || 0;
          const price = parseFloat(priceInput.value) || 0;
          const unit = unitSelect.value || "";
          const totalValue = stock * price;

          totalStockDisplay.textContent =
            stock.toLocaleString() + (unit ? " " + unit : "");
          reorderLevelDisplay.textContent =
            reorder.toLocaleString() + (unit ? " " + unit : "");
          totalValueDisplay.textContent = new Intl.NumberFormat("en-NG", {
            style: "currency",
            currency: "NGN",
          }).format(totalValue);

          // Add warning if stock is below reorder level
          if (stock > 0 && reorder > 0 && stock <= reorder) {
            totalStockDisplay.className = "text-danger";
            totalStockDisplay.parentElement.style.backgroundColor = "#ffebee";
          } else {
            totalStockDisplay.className = "text-primary";
            totalStockDisplay.parentElement.style.backgroundColor =
              "transparent";
          }
        }

        // Add event listeners
        [stockInput, reorderInput, priceInput, unitSelect].forEach((input) => {
          input.addEventListener("input", updateDisplays);
          input.addEventListener("change", updateDisplays);
        });

        // Form submission enhancement
        const form = document.getElementById("feedDetailsForm");
        form.addEventListener("submit", function (e) {
          const submitBtn = form.querySelector('button[type="submit"]');
          submitBtn.innerHTML = '<span class="loading"></span> Updating...';
          submitBtn.disabled = true;
        });

        // Initialize displays
        updateDisplays();
      });
    </script>
  </body>
</html>
