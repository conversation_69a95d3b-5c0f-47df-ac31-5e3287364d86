const mongoose = require('mongoose');

const supplierSchema = new mongoose.Schema({
  supplier_id: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: true
  },
  address: {
    type: String,
    required: true
  },
  account_balance: {
    type: Number,
    required: true
  },
  account_no: {
    type: Number,
    required: true
  },
  bank: {
    type: String,
    required: true
  }
}, {
  timestamps: true
});

module.exports = mongoose.model('Supplier', supplierSchema);
