<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/global-styles.css" />
    <script src="/javascript/salesreport.js" defer></script>
    <title>Sales Report - Farm Management System</title>
  </head>
  <body>
    <!-- Modern Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="app-logo">Farm Management System</div>
        <div class="user-info">
          <span>Sales Report</span>
        </div>
      </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav no-print">
      <div class="breadcrumb-content">
        <a href="/management/homepage" class="breadcrumb-item">Dashboard</a>
        <span class="breadcrumb-separator">›</span>
        <a href="/management/homepage" class="breadcrumb-item">Reports</a>
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-item active">Sales Report</span>
      </div>
    </nav>

    <!-- Main Container -->
    <div class="app-container">
      <!-- Alerts -->
      <% if(hasErr){%>
      <div class="alert alert-danger">
        <div>
          <% errMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%> <% if(hasSuccess){%>
      <div class="alert alert-success">
        <div>
          <% successMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%>

      <!-- Page Header -->
      <div class="page-header no-print">
        <h1 class="page-title">💰 Sales Report</h1>
        <p class="page-subtitle">
          Comprehensive sales analytics and customer insights
        </p>
        <div class="page-actions">
          <a href="/management/homepage" class="btn btn-secondary btn-back"
            >Back to Dashboard</a
          >
          <a href="/record/sales" class="btn btn-success btn-add"
            >Add New Sale</a
          >
          <button id="printBtn" class="btn btn-outline btn-print no-print">
            Print Report
          </button>
        </div>
      </div>

      <!-- Date Range Filter -->
      <div class="card mb-4 no-print">
        <div class="card-header">
          <h3 class="card-title">📅 Filter by Date Range</h3>
        </div>
        <div class="card-body">
          <form id="dateFilterForm" class="form-grid">
            <div class="form-group">
              <label for="from" class="form-label">📅 From Date</label>
              <input
                type="date"
                class="form-control"
                name="from"
                id="from"
                required
              />
            </div>
            <div class="form-group">
              <label for="to" class="form-label">📅 To Date</label>
              <input
                type="date"
                class="form-control"
                name="to"
                id="to"
                required
              />
            </div>
            <div class="form-group d-flex align-center">
              <button
                type="submit"
                class="btn btn-primary btn-search"
                id="submit"
              >
                🔍 Generate Report
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Report Data Table -->
      <div class="table-container" id="printableSection">
        <div class="table-header">
          <h3 class="table-title">
            Desire's Poultry Sales Report For: <%= new
            Date().toLocaleDateString() %> <%= new Date().toLocaleTimeString()
            %>
          </h3>
          <div class="table-actions no-print">
            <span class="text-muted"
              ><%= Sales.length %> sales records found</span
            >
          </div>
        </div>

        <table class="data-table" id="main-table">
          <thead>
            <tr>
              <th>Date</th>
              <th>Customer</th>
              <th>Big Egg</th>
              <th>Big Egg P</th>
              <th>Small Egg</th>
              <th>Small Egg P</th>
              <th>Broken Egg</th>
              <th>Broken Egg P</th>
              <th>Total Amount</th>
              <th>Remarks</th>
              <th>Staff Name</th>
              <th>Staff No</th>
            </tr>
          </thead>
          <tbody>
            <% if(Sales && Sales.length > 0) { %> <%
            Sales.forEach(function(sales,index){ %>
            <tr
              style="
                animation: fadeIn 0.3s ease-in-out <%= index * 0.1 %> s both;
              "
            >
              <td><%= new Date(sales.date).toLocaleDateString() %></td>
              <td><strong><%= sales.customer %></strong></td>
              <td>
                <span class="status-indicator status-info">
                  <%= sales.big_egg_crate %>
                </span>
              </td>
              <td>₦<%= Number(sales.big_egg_price).toLocaleString() %></td>
              <td>
                <span class="status-indicator status-info">
                  <%= sales.small_egg_crate %>
                </span>
              </td>
              <td>₦<%= Number(sales.small_egg_price).toLocaleString() %></td>
              <td>
                <span class="status-indicator status-info">
                  <%= sales.broken_egg_crate %>
                </span>
              </td>
              <td>₦<%= Number(sales.broken_egg_price).toLocaleString() %></td>
              <td>
                <strong
                  >₦<%= Number(sales.total_price).toLocaleString() %></strong
                >
              </td>
              <td><%= sales.remark || '-' %></td>
              <td><%= sales.staff_name %></td>
              <td><%= sales.staff_no %></td>
            </tr>
            <% }) %> <% } else { %>
            <tr>
              <td colspan="9" class="text-center p-4">
                <div class="text-muted">
                  <h5>📭 No Sales Records Found</h5>
                  <p>No sales records found for the selected date range.</p>
                  <a href="/record/sales" class="btn btn-primary btn-sm">
                    ➕ Create First Sale
                  </a>
                </div>
              </td>
            </tr>
            <% } %>
          </tbody>
        </table>

        <!-- Summary Table -->
        <div id="display-tr" class="mt-4"></div>
      </div>
    </div>

    <!-- Enhanced JavaScript -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const fromInput = document.getElementById("from");
        const toInput = document.getElementById("to");

        // Set default date range (last 30 days)
        const today = new Date();
        const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

        toInput.value = today.toISOString().split("T")[0];
        fromInput.value = lastMonth.toISOString().split("T")[0];

        // Print button functionality
        const printBtn = document.getElementById("printBtn");
        if (printBtn) {
          printBtn.addEventListener("click", function () {
            window.print();
          });
        }
      });
    </script>

    <!-- Add CSS animations -->
    <style>
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .status-indicator {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-weight: 600;
      }

      .status-info {
        background: #e3f2fd;
        color: #1565c0;
      }
    </style>
  </body>
</html>
