<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/global-styles.css" />
    <title>Update Customer Account - Farm Management System</title>
  </head>
  <body>
    <!-- Modern Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="app-logo">Farm Management System</div>
        <div class="user-info">
          <span>Update Customer Account</span>
        </div>
      </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav">
      <div class="breadcrumb-content">
        <a href="/management/homepage" class="breadcrumb-item">Dashboard</a>
        <span class="breadcrumb-separator">›</span>
        <a href="/management/homepage" class="breadcrumb-item">Management</a>
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-item active">Update Customer</span>
      </div>
    </nav>

    <!-- Main Container -->
    <div class="app-container">
      <!-- Alerts -->
      <% if(hasErr){%>
      <div class="alert alert-danger">
        <div>
          <% errMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%> <% if(hasSuccess){%>
      <div class="alert alert-success">
        <div>
          <% successMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%>

      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">✏️ Update Customer Account</h1>
        <p class="page-subtitle">Search and update customer information</p>
        <div class="page-actions">
          <a href="/management/homepage" class="btn btn-secondary btn-back"
            >Back to Dashboard</a
          >
          <a href="/management/check-customers" class="btn btn-outline"
            >View All Customers</a
          >
          <a href="/management/createcustomer" class="btn btn-success"
            >Create New Customer</a
          >
        </div>
      </div>

      <!-- STEP 1: Search Customer Section -->
      <div class="card mb-4" id="searchSection">
        <div class="card-header">
          <h3 class="card-title">🔍 Step 1: Search Customer</h3>
          <p class="card-subtitle">
            Enter the customer name to find and update their information
          </p>
        </div>
        <div class="card-body">
          <div class="search-container">
            <div class="form-group">
              <label for="searchName" class="form-label"
                >👤 Customer Name</label
              >
              <div class="search-input-group">
                <input
                  type="text"
                  class="form-control"
                  id="searchName"
                  placeholder="Enter customer full name to search..."
                  autocomplete="off"
                />
                <button type="button" class="btn btn-primary" id="searchBtn">
                  🔍 Search Customer
                </button>
              </div>
              <small class="text-muted"
                >Enter the exact name as registered in the system</small
              >
            </div>

            <!-- Search Results -->
            <div id="searchResults" class="search-results d-none">
              <div class="alert alert-info">
                <div id="searchResultContent"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- STEP 2: Update Customer Form (Initially Hidden) -->
      <div class="card mb-4 d-none" id="updateSection">
        <div class="card-header">
          <h3 class="card-title">✏️ Step 2: Update Customer Information</h3>
          <p class="card-subtitle">Modify the customer's details below</p>
        </div>
        <div class="card-body">
          <form
            action="/management/updatecustomer"
            method="POST"
            id="updateCustomerForm"
          >
            <!-- Current Customer Info Display -->
            <div class="alert alert-success mb-4" id="currentCustomerInfo">
              <h5>📋 Current Customer Information:</h5>
              <div id="currentCustomerDetails"></div>
            </div>

            <!-- Hidden field for old name -->
            <input type="hidden" name="oldname" id="oldname" />

            <!-- Customer Information -->
            <div class="card mb-4">
              <div class="card-header">
                <h4 class="card-title">👤 Customer Information</h4>
              </div>
              <div class="card-body">
                <div class="form-grid">
                  <div class="form-group">
                    <label for="name" class="form-label"
                      >👤 Customer Name</label
                    >
                    <input
                      type="text"
                      class="form-control"
                      name="name"
                      id="name"
                      placeholder="Enter customer full name"
                      required
                    />
                  </div>

                  <div class="form-group" style="grid-column: 1 / -1">
                    <label for="address" class="form-label"
                      >📍 Customer Address</label
                    >
                    <textarea
                      class="form-control"
                      name="address"
                      id="address"
                      placeholder="Enter customer complete address"
                      rows="3"
                      required
                    ></textarea>
                  </div>
                </div>
              </div>
            </div>

            <!-- Financial Information -->
            <div class="card mb-4">
              <div class="card-header">
                <h4 class="card-title">💰 Financial Information</h4>
              </div>
              <div class="card-body">
                <div class="form-grid">
                  <div class="form-group">
                    <label for="account_balance" class="form-label"
                      >💰 Account Balance (₦)</label
                    >
                    <input
                      type="number"
                      class="form-control"
                      name="account_balance"
                      id="account_balance"
                      placeholder="Enter account balance"
                      min="0"
                      step="0.01"
                      required
                    />
                  </div>

                  <div class="form-group">
                    <label for="account_no" class="form-label"
                      >🏦 Account Number</label
                    >
                    <input
                      type="text"
                      class="form-control"
                      name="account_no"
                      id="account_no"
                      placeholder="Enter bank account number"
                      required
                    />
                  </div>

                  <div class="form-group">
                    <label for="bank" class="form-label">🏛️ Bank Name</label>
                    <input
                      type="text"
                      class="form-control"
                      name="bank"
                      id="bank"
                      placeholder="Enter bank name"
                      required
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- Update Summary -->
            <div class="card mb-4">
              <div class="card-header">
                <h4 class="card-title">📊 Update Summary</h4>
              </div>
              <div class="card-body">
                <div class="grid grid-4">
                  <div class="text-center">
                    <h5 class="text-primary" id="summaryName">-</h5>
                    <p class="text-muted">Customer Name</p>
                  </div>
                  <div class="text-center">
                    <h5 class="text-success" id="summaryBalance">₦0.00</h5>
                    <p class="text-muted">Account Balance</p>
                  </div>
                  <div class="text-center">
                    <h5 class="text-info" id="summaryBank">-</h5>
                    <p class="text-muted">Bank</p>
                  </div>
                  <div class="text-center">
                    <h5 class="text-warning" id="summaryAccount">-</h5>
                    <p class="text-muted">Account Number</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="d-flex justify-between align-center">
              <div>
                <button
                  type="button"
                  class="btn btn-secondary"
                  id="backToSearchBtn"
                >
                  ← Back to Search
                </button>
              </div>
              <div class="d-flex gap-2">
                <button type="reset" class="btn btn-outline">
                  🔄 Reset Changes
                </button>
                <button type="submit" class="btn btn-success btn-save">
                  ✏️ Update Customer
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Enhanced JavaScript -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const searchSection = document.getElementById("searchSection");
        const updateSection = document.getElementById("updateSection");
        const searchBtn = document.getElementById("searchBtn");
        const searchName = document.getElementById("searchName");
        const searchResults = document.getElementById("searchResults");
        const searchResultContent = document.getElementById(
          "searchResultContent"
        );
        const backToSearchBtn = document.getElementById("backToSearchBtn");
        const updateForm = document.getElementById("updateCustomerForm");

        let foundCustomer = null;

        // Search functionality
        searchBtn.addEventListener("click", function () {
          const name = searchName.value.trim();
          if (!name) {
            alert("Please enter a customer name to search");
            return;
          }

          // Show loading
          searchBtn.innerHTML = '<span class="loading"></span> Searching...';
          searchBtn.disabled = true;

          // Real API call to search customer by name
          fetch(`/management/searchcustomer/${encodeURIComponent(name)}`)
            .then((response) => response.json())
            .then((data) => {
              if (data.found) {
                foundCustomer = data.customer;
                showSearchResult(true, data.customer);
              } else {
                foundCustomer = null;
                showSearchResult(false, null);
              }
            })
            .catch((error) => {
              console.error("Error searching for customer:", error);
              foundCustomer = null;
              showSearchResult(false, null);
            })
            .finally(() => {
              // Reset button
              searchBtn.innerHTML = "🔍 Search Customer";
              searchBtn.disabled = false;
            });
        });

        function showSearchResult(found, customer) {
          searchResults.classList.remove("d-none");

          if (found) {
            searchResultContent.innerHTML = `
              <div class="d-flex justify-between align-center">
                <div>
                  <h5 class="text-success mb-1">✅ Customer Found!</h5>
                  <p class="mb-0"><strong>Name:</strong> ${customer.name}</p>
                  <p class="mb-0"><strong>Balance:</strong> ₦${customer.account_balance.toLocaleString()}</p>
                  <p class="mb-0"><strong>Bank:</strong> ${customer.bank}</p>
                </div>
                <button type="button" class="btn btn-success" id="proceedToUpdateBtn">
                  Proceed to Update →
                </button>
              </div>
            `;

            // Add proceed button functionality
            document
              .getElementById("proceedToUpdateBtn")
              .addEventListener("click", function () {
                showUpdateForm(customer);
              });
          } else {
            searchResultContent.innerHTML = `
              <div class="text-center">
                <h5 class="text-danger mb-1">❌ Customer Not Found</h5>
                <p class="mb-0">No customer found with the name "${searchName.value}"</p>
                <p class="mb-0 text-muted">Please check the spelling and try again</p>
              </div>
            `;
          }
        }

        function showUpdateForm(customer) {
          // Hide search section and show update section
          searchSection.classList.add("d-none");
          updateSection.classList.remove("d-none");

          // Populate current customer info
          document.getElementById("currentCustomerDetails").innerHTML = `
            <div class="row">
              <div class="col-md-6">
                <p><strong>Name:</strong> ${customer.name}</p>
                <p><strong>Address:</strong> ${customer.address}</p>
                <p><strong>Balance:</strong> ₦${customer.account_balance.toLocaleString()}</p>
              </div>
              <div class="col-md-6">
                <p><strong>Account No:</strong> ${customer.account_no}</p>
                <p><strong>Bank:</strong> ${customer.bank}</p>
              </div>
            </div>
          `;

          // Populate form fields
          document.getElementById("oldname").value = customer.name;
          document.getElementById("name").value = customer.name;
          document.getElementById("address").value = customer.address;
          document.getElementById("account_balance").value =
            customer.account_balance;
          document.getElementById("account_no").value = customer.account_no;
          document.getElementById("bank").value = customer.bank;

          // Update summary
          updateSummary();
        }

        // Back to search functionality
        backToSearchBtn.addEventListener("click", function () {
          updateSection.classList.add("d-none");
          searchSection.classList.remove("d-none");
          searchResults.classList.add("d-none");
          searchName.value = "";
          updateForm.reset();
        });

        // Update summary function
        function updateSummary() {
          const name = document.getElementById("name").value || "-";
          const balance =
            parseFloat(document.getElementById("account_balance").value) || 0;
          const bank = document.getElementById("bank").value || "-";
          const accountNo = document.getElementById("account_no").value || "-";

          document.getElementById("summaryName").textContent = name;
          document.getElementById("summaryBalance").textContent =
            new Intl.NumberFormat("en-NG", {
              style: "currency",
              currency: "NGN",
            }).format(balance);
          document.getElementById("summaryBank").textContent = bank;
          document.getElementById("summaryAccount").textContent = accountNo;
        }

        // Add event listeners for real-time updates
        ["name", "account_balance", "bank", "account_no"].forEach((id) => {
          document.getElementById(id).addEventListener("input", updateSummary);
        });

        // Form submission enhancement
        updateForm.addEventListener("submit", function (e) {
          const submitBtn = updateForm.querySelector('button[type="submit"]');
          submitBtn.innerHTML = '<span class="loading"></span> Updating...';
          submitBtn.disabled = true;
        });

        // Enter key search
        searchName.addEventListener("keypress", function (e) {
          if (e.key === "Enter") {
            searchBtn.click();
          }
        });
      });
    </script>
  </body>
</html>
