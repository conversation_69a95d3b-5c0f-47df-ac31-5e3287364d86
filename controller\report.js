const DailyProductionRecord = require("../model/DailyProductionRecord");
const MedicationRecord = require("../model/MedicationRecord");
const SalesRecord = require("../model/SalesRecord");
const SuppliesRecord = require("../model/SuppliesRecord");

const getdailyProductionRecord = async (req, res) => {
  try {
    const { date_from, date_to } = req.body;
    const foundRecord = await DailyProductionRecord.find({
      date: {
        $gte: new Date(date_from),
        $lte: new Date(date_to),
      },
    }).sort({ date: 1 });
    if (!foundRecord || foundRecord.length === 0) {
      return res.json([]);
    }
    return res.json(foundRecord);
  } catch (err) {
    console.log(err);
    return res.json(err.message);
  }
};

const getMedicationRecord = async (req, res) => {
  try {
    const { date_from, date_to } = req.body;
    const foundRecord = await MedicationRecord.find({
      date: {
        $gte: new Date(date_from),
        $lte: new Date(date_to),
      },
    }).sort({ date: 1 });
    if (!foundRecord || foundRecord.length === 0) {
      return res.json([]);
    }
    return res.json(foundRecord);
  } catch (err) {
    console.log(err);
    return res.json(err.message);
  }
};

const getSuppliesRecord = async (req, res) => {
  try {
    const { date_from, date_to } = req.body;
    const foundRecord = await SuppliesRecord.find({
      date: {
        $gte: new Date(date_from),
        $lte: new Date(date_to),
      },
    }).sort({ date: 1 });
    if (!foundRecord || foundRecord.length === 0) {
      return res.json([]);
    }
    return res.json(foundRecord);
  } catch (err) {
    console.log(err);
    return res.json(err.message);
  }
};

const getSalesRecord = async (req, res) => {
  try {
    const { date_from, date_to } = req.body;
    const foundRecord = await SalesRecord.find({
      date: {
        $gte: new Date(date_from),
        $lte: new Date(date_to),
      },
    }).sort({ date: 1 });
    if (!foundRecord || foundRecord.length === 0) {
      return res.json([]);
    }
    return res.json(foundRecord);
  } catch (err) {
    console.log(err);
    return res.json(err.message);
  }
};
module.exports = {
  getdailyProductionRecord,
  getMedicationRecord,
  getSuppliesRecord,
  getSalesRecord,
};
