const refreshBtn = document.getElementById("refreshBtn");
const dataTableBody = document.getElementById("data-table-body");
const printBtn = document.getElementById("printbtn");
const searchDiv = document.getElementById("searchdiv");

// Function to load bird pen data
function loadBirdPenData() {
  fetch("/management/allbirdpen")
    .then((res) => res.json())
    .then((data) => {
      let htmlContent = "";
      if (data && data.length > 0) {
        data.forEach((record) => {
          const dateFormatted = record.date
            ? new Date(record.date).toLocaleDateString()
            : "N/A";
          const occupancyPercent = () => {
            let expectedBirds = record.no_of_cage * record.cage_capacity;
            let divResult = record.stock / expectedBirds;
            let mainResult = divResult * 100;
            return mainResult.toFixed(1);
          };
          // Color code based on occupancy
          let occupancyColor = "";
          if (occupancyPercent() >= 90) {
            occupancyColor =
              ' style="background-color: #ffebee; color: #c62828;"'; // Red for overcrowded
          } else if (occupancyPercent() >= 75) {
            occupancyColor =
              ' style="background-color: #fff3e0; color: #ef6c00;"'; // Orange for high occupancy
          } else if (occupancyPercent() >= 50) {
            occupancyColor =
              ' style="background-color: #e8f5e8; color: #2e7d32;"'; // Green for good occupancy
          }

          htmlContent += `
            <tr${occupancyColor}>
              <td>${record.code}</td>
              <td>${record.name}</td>
              <td>${record.type || "N/A"}</td>
              <td>${dateFormatted}</td>
              <td>${record.no_of_cage}</td>
              <td>${record.cage_capacity}</td>
              <td>${record.stock}</td>
              <td>${record.batch}</td>
              <td>${occupancyPercent()}%</td>
            </tr>
          `;
        });
      } else {
        htmlContent = `
          <tr>
            <td colspan="8" style="text-align: center; padding: 20px;">
              No bird pen records found
            </td>
          </tr>
        `;
      }
      dataTableBody.innerHTML = htmlContent;
    })
    .catch((error) => {
      console.error("Error loading bird pen data:", error);
      dataTableBody.innerHTML = `
        <tr>
          <td colspan="8" style="text-align: center; padding: 20px; color: red;">
            Error loading data. Please try again.
          </td>
        </tr>
      `;
    });
}

// Load data when page loads
document.addEventListener("DOMContentLoaded", loadBirdPenData);

// Refresh button event listener
refreshBtn.addEventListener("click", (e) => {
  e.preventDefault();
  loadBirdPenData();
});

// Print button event listener
printBtn.addEventListener("click", (e) => {
  e.preventDefault();
  searchDiv.style.display = "none";
  printBtn.style.display = "none";
  window.print();
  // Restore elements after printing
  setTimeout(() => {
    searchDiv.style.display = "block";
    printBtn.style.display = "block";
  }, 1000);
});
