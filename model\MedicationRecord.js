const mongoose = require('mongoose');

const medicationRecordSchema = new mongoose.Schema({
  pen: {
    type: String,
    required: true
  },
  date: {
    type: Date,
    required: true
  },
  stock_code: {
    type: Number,
    required: true
  },
  medication_desc: {
    type: String,
    required: true
  },
  drug: {
    type: String,
    required: true
  },
  supplier: {
    type: String,
    required: true
  },
  quantity: {
    type: Number,
    required: true
  },
  price: {
    type: Number,
    required: true
  },
  amount: {
    type: Number,
    required: true
  }
}, {
  timestamps: true
});

module.exports = mongoose.model('MedicationRecord', medicationRecordSchema);
