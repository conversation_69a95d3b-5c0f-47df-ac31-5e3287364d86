<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/global-styles.css" />
    <title>Update Supplier Account - Farm Management System</title>
  </head>
  <body>
    <!-- Modern Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="app-logo">Farm Management System</div>
        <div class="user-info">
          <span>Update Supplier Account</span>
        </div>
      </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav">
      <div class="breadcrumb-content">
        <a href="/management/homepage" class="breadcrumb-item">Dashboard</a>
        <span class="breadcrumb-separator">›</span>
        <a href="/management/homepage" class="breadcrumb-item">Management</a>
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-item active">Update Supplier</span>
      </div>
    </nav>

    <!-- Main Container -->
    <div class="app-container">
      <!-- Alerts -->
      <% if(hasErr){%>
      <div class="alert alert-danger">
        <div>
          <% errMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%> <% if(hasSuccess){%>
      <div class="alert alert-success">
        <div>
          <% successMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%>

      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">✏️ Update Supplier Account</h1>
        <p class="page-subtitle">Search and update supplier information</p>
        <div class="page-actions">
          <a href="/management/homepage" class="btn btn-secondary btn-back"
            >Back to Dashboard</a
          >
          <a href="/management/check-suppliers" class="btn btn-outline"
            >View All Suppliers</a
          >
          <a href="/management/createsupplier" class="btn btn-success"
            >Create New Supplier</a
          >
        </div>
      </div>

      <!-- STEP 1: Search Supplier Section -->
      <div class="card mb-4" id="searchSection">
        <div class="card-header">
          <h3 class="card-title">🔍 Step 1: Search Supplier</h3>
          <p class="card-subtitle">
            Enter the supplier name to find and update their information
          </p>
        </div>
        <div class="card-body">
          <div class="search-container">
            <div class="form-group">
              <label for="searchName" class="form-label"
                >🏭 Supplier Name</label
              >
              <div class="search-input-group">
                <input
                  type="text"
                  class="form-control"
                  id="searchName"
                  placeholder="Enter supplier company name to search..."
                  autocomplete="off"
                />
                <button type="button" class="btn btn-primary" id="searchBtn">
                  🔍 Search Supplier
                </button>
              </div>
              <small class="text-muted"
                >Enter the exact company name as registered in the system</small
              >
            </div>

            <!-- Search Results -->
            <div id="searchResults" class="search-results d-none">
              <div class="alert alert-info">
                <div id="searchResultContent"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- STEP 2: Update Supplier Form (Initially Hidden) -->
      <div class="card mb-4 d-none" id="updateSection">
        <div class="card-header">
          <h3 class="card-title">✏️ Step 2: Update Supplier Information</h3>
          <p class="card-subtitle">Modify the supplier's details below</p>
        </div>
        <div class="card-body">
          <form
            action="/management/updatesupplier"
            method="POST"
            id="updateSupplierForm"
          >
            <!-- Current Supplier Info Display -->
            <div class="alert alert-success mb-4" id="currentSupplierInfo">
              <h5>📋 Current Supplier Information:</h5>
              <div id="currentSupplierDetails"></div>
            </div>

            <!-- Supplier Identification -->
            <div class="card mb-4">
              <div class="card-header">
                <h4 class="card-title">🏷️ Supplier Identification</h4>
              </div>
              <div class="card-body">
                <div class="form-grid">
                  <div class="form-group">
                    <label for="supplier_id" class="form-label"
                      >🏷️ Supplier ID</label
                    >
                    <select
                      name="supplier_id"
                      class="form-control form-select"
                      id="supplier_id"
                      required
                    >
                      <option value="">Select supplier ID...</option>
                      <option value="CHI">CHI - Chi Farms Limited</option>
                      <option value="OBA">OBA - Oba Poultry Farms</option>
                      <option value="ANC">ANC - Anchor Farms Nigeria</option>
                      <option value="FSI">FSI - Farm Solutions Inc</option>
                    </select>
                  </div>

                  <div class="form-group">
                    <label for="name" class="form-label"
                      >🏭 Supplier Name</label
                    >
                    <input
                      type="text"
                      class="form-control"
                      name="name"
                      id="name"
                      placeholder="Enter supplier company name"
                      required
                      readonly
                    />
                    <small class="text-muted"
                      >Name cannot be changed (used for identification)</small
                    >
                  </div>
                </div>
              </div>
            </div>

            <!-- Supplier Information -->
            <div class="card mb-4">
              <div class="card-header">
                <h4 class="card-title">🏭 Supplier Information</h4>
              </div>
              <div class="card-body">
                <div class="form-group" style="grid-column: 1 / -1">
                  <label for="address" class="form-label"
                    >📍 Supplier Address</label
                  >
                  <textarea
                    class="form-control"
                    name="address"
                    id="address"
                    placeholder="Enter supplier complete address"
                    rows="3"
                    required
                  ></textarea>
                </div>
              </div>
            </div>

            <!-- Financial Information -->
            <div class="card mb-4">
              <div class="card-header">
                <h4 class="card-title">💰 Financial Information</h4>
              </div>
              <div class="card-body">
                <div class="form-grid">
                  <div class="form-group">
                    <label for="account_balance" class="form-label"
                      >💰 Account Balance (₦)</label
                    >
                    <input
                      type="number"
                      class="form-control"
                      name="account_balance"
                      id="account_balance"
                      placeholder="Enter account balance"
                      min="0"
                      step="0.01"
                      required
                    />
                  </div>

                  <div class="form-group">
                    <label for="account_no" class="form-label"
                      >🏦 Account Number</label
                    >
                    <input
                      type="text"
                      class="form-control"
                      name="account_no"
                      id="account_no"
                      placeholder="Enter bank account number"
                      required
                    />
                  </div>

                  <div class="form-group">
                    <label for="bank" class="form-label">🏛️ Bank Name</label>
                    <input
                      type="text"
                      class="form-control"
                      name="bank"
                      id="bank"
                      placeholder="Enter bank name"
                      required
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- Update Summary -->
            <div class="card mb-4">
              <div class="card-header">
                <h4 class="card-title">📊 Update Summary</h4>
              </div>
              <div class="card-body">
                <div class="grid grid-4">
                  <div class="text-center">
                    <h5 class="text-primary" id="summarySupplierID">-</h5>
                    <p class="text-muted">Supplier ID</p>
                  </div>
                  <div class="text-center">
                    <h5 class="text-info" id="summaryName">-</h5>
                    <p class="text-muted">Company Name</p>
                  </div>
                  <div class="text-center">
                    <h5 class="text-success" id="summaryBalance">₦0.00</h5>
                    <p class="text-muted">Account Balance</p>
                  </div>
                  <div class="text-center">
                    <h5 class="text-warning" id="summaryBank">-</h5>
                    <p class="text-muted">Bank</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="d-flex justify-between align-center">
              <div>
                <button
                  type="button"
                  class="btn btn-secondary"
                  id="backToSearchBtn"
                >
                  ← Back to Search
                </button>
              </div>
              <div class="d-flex gap-2">
                <button type="reset" class="btn btn-outline">
                  🔄 Reset Changes
                </button>
                <button type="submit" class="btn btn-success btn-save">
                  ✏️ Update Supplier
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Enhanced JavaScript -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const searchSection = document.getElementById("searchSection");
        const updateSection = document.getElementById("updateSection");
        const searchBtn = document.getElementById("searchBtn");
        const searchName = document.getElementById("searchName");
        const searchResults = document.getElementById("searchResults");
        const searchResultContent = document.getElementById(
          "searchResultContent"
        );
        const backToSearchBtn = document.getElementById("backToSearchBtn");
        const updateForm = document.getElementById("updateSupplierForm");

        let foundSupplier = null;

        // Search functionality
        searchBtn.addEventListener("click", function () {
          const name = searchName.value.trim();
          if (!name) {
            alert("Please enter a supplier name to search");
            return;
          }

          // Show loading
          searchBtn.innerHTML = '<span class="loading"></span> Searching...';
          searchBtn.disabled = true;

          // Real API call to search supplier by name
          fetch(`/management/searchsupplier/${encodeURIComponent(name)}`)
            .then((response) => response.json())
            .then((data) => {
              if (data.found) {
                foundSupplier = data.supplier;
                showSearchResult(true, data.supplier);
              } else {
                foundSupplier = null;
                showSearchResult(false, null);
              }
            })
            .catch((error) => {
              console.error("Error searching for supplier:", error);
              foundSupplier = null;
              showSearchResult(false, null);
            })
            .finally(() => {
              // Reset button
              searchBtn.innerHTML = "🔍 Search Supplier";
              searchBtn.disabled = false;
            });
        });

        function showSearchResult(found, supplier) {
          searchResults.classList.remove("d-none");

          if (found) {
            searchResultContent.innerHTML = `
              <div class="d-flex justify-between align-center">
                <div>
                  <h5 class="text-success mb-1">✅ Supplier Found!</h5>
                  <p class="mb-0"><strong>Name:</strong> ${supplier.name}</p>
                  <p class="mb-0"><strong>ID:</strong> ${
                    supplier.supplier_id
                  }</p>
                  <p class="mb-0"><strong>Balance:</strong> ₦${supplier.account_balance.toLocaleString()}</p>
                </div>
                <button type="button" class="btn btn-success" id="proceedToUpdateBtn">
                  Proceed to Update →
                </button>
              </div>
            `;

            // Add proceed button functionality
            document
              .getElementById("proceedToUpdateBtn")
              .addEventListener("click", function () {
                showUpdateForm(supplier);
              });
          } else {
            searchResultContent.innerHTML = `
              <div class="text-center">
                <h5 class="text-danger mb-1">❌ Supplier Not Found</h5>
                <p class="mb-0">No supplier found with the name "${searchName.value}"</p>
                <p class="mb-0 text-muted">Please check the spelling and try again</p>
              </div>
            `;
          }
        }

        function showUpdateForm(supplier) {
          // Hide search section and show update section
          searchSection.classList.add("d-none");
          updateSection.classList.remove("d-none");

          // Populate current supplier info
          document.getElementById("currentSupplierDetails").innerHTML = `
            <div class="row">
              <div class="col-md-6">
                <p><strong>Supplier ID:</strong> ${supplier.supplier_id}</p>
                <p><strong>Name:</strong> ${supplier.name}</p>
                <p><strong>Address:</strong> ${supplier.address}</p>
              </div>
              <div class="col-md-6">
                <p><strong>Balance:</strong> ₦${supplier.account_balance.toLocaleString()}</p>
                <p><strong>Account No:</strong> ${supplier.account_no}</p>
                <p><strong>Bank:</strong> ${supplier.bank}</p>
              </div>
            </div>
          `;

          // Populate form fields
          document.getElementById("supplier_id").value = supplier.supplier_id;
          document.getElementById("name").value = supplier.name;
          document.getElementById("address").value = supplier.address;
          document.getElementById("account_balance").value =
            supplier.account_balance;
          document.getElementById("account_no").value = supplier.account_no;
          document.getElementById("bank").value = supplier.bank;

          // Update summary
          updateSummary();
        }

        // Back to search functionality
        backToSearchBtn.addEventListener("click", function () {
          updateSection.classList.add("d-none");
          searchSection.classList.remove("d-none");
          searchResults.classList.add("d-none");
          searchName.value = "";
          updateForm.reset();
        });

        // Update summary function
        function updateSummary() {
          const supplierId =
            document.getElementById("supplier_id").value || "-";
          const name = document.getElementById("name").value || "-";
          const balance =
            parseFloat(document.getElementById("account_balance").value) || 0;
          const bank = document.getElementById("bank").value || "-";

          document.getElementById("summarySupplierID").textContent = supplierId;
          document.getElementById("summaryName").textContent = name;
          document.getElementById("summaryBalance").textContent =
            new Intl.NumberFormat("en-NG", {
              style: "currency",
              currency: "NGN",
            }).format(balance);
          document.getElementById("summaryBank").textContent = bank;
        }

        // Add event listeners for real-time updates
        ["supplier_id", "account_balance", "bank"].forEach((id) => {
          document.getElementById(id).addEventListener("change", updateSummary);
          document.getElementById(id).addEventListener("input", updateSummary);
        });

        // Form submission enhancement
        updateForm.addEventListener("submit", function (e) {
          const submitBtn = updateForm.querySelector('button[type="submit"]');
          submitBtn.innerHTML = '<span class="loading"></span> Updating...';
          submitBtn.disabled = true;
        });

        // Enter key search
        searchName.addEventListener("keypress", function (e) {
          if (e.key === "Enter") {
            searchBtn.click();
          }
        });
      });
    </script>
  </body>
</html>
