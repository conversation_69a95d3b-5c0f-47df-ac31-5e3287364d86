<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Delete birdintake</title>
    <link
      href="https://fonts.googleapis.com/icon?family=Material+Icons"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="/stylesheets/global-styles.css" />
    <script src="/javascript/deleterecord/birdintake.js" defer></script>
  </head>
  <body>
    <!-- Modern Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="app-logo">Farm Management System</div>
        <div class="user-info">
          <span> Delete Birdintake Record</span>
        </div>
      </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav">
      <div class="breadcrumb-content">
        <a href="/management/homepage" class="breadcrumb-item"
          >Management Dashboard</a
        >
        <span class="breadcrumb-separator">›</span>
        <a href="/deleterecord" class="breadcrumb-item"
          >Delete Record Dashboard</a
        >
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-item active">Record Details</span>
      </div>
    </nav>

    <!-- Alerts -->
    <% if(hasErr){%>
    <div class="alert alert-danger">
      <div>
        <% errMsg.forEach(message =>{ %>
        <p class="mb-0"><%= message%></p>
        <% })%>
      </div>
    </div>
    <%}%> <% if(hasSuccess){%>
    <div class="alert alert-success">
      <div>
        <% successMsg.forEach(message =>{ %>
        <p class="mb-0"><%= message%></p>
        <% })%>
      </div>
    </div>
    <%}%>

    <!-- Page Header -->
    <div class="page-header">
      <h1 class="page-title">📊 Delete birdintake Records</h1>
      <p class="page-subtitle">Delete birdintake record from record list ...</p>
      <div class="page-actions">
        <a href="/deleterecord/" class="btn btn-secondary btn-back"
          >Back to Delete Dashboard</a
        >
      </div>
    </div>

    <!-- Date Range Filter -->
    <div class="card mb-4 no-print">
      <div class="card-header">
        <h3 class="card-title">📅 Filter by Date Range</h3>
      </div>
      <div class="card-body">
        <form id="dateFilterForm" class="form-grid">
          <div class="form-group">
            <label for="from" class="form-label">📅 From Date</label>
            <input
              type="date"
              class="form-control"
              name="from"
              id="from"
              required
            />
          </div>
          <div class="form-group">
            <label for="to" class="form-label">📅 To Date</label>
            <input
              type="date"
              class="form-control"
              name="to"
              id="to"
              required
            />
          </div>
          <div class="form-group d-flex align-center">
            <button
              type="submit"
              class="btn btn-primary btn-search"
              id="submit"
            >
              🔍 Generate Report
            </button>
          </div>
        </form>
      </div>
    </div>
    <!-- record list container -->
    <div class="record-container" id="record-container">
      <div class="record-header" id="record-header">
        <p>Batch code</p>
        <p>Type</p>
        <p>Date taken</p>
        <p>No Of Bird</p>
        <p>Pen</p>
        <p>Supplier</p>
        <p>Price</p>
        <p>Total amount</p>
        <p>Level</p>
        <p>Staff name</p>
        <span style="width: 48px"></span>
      </div>
      <% if(hasBirdIntake){ %> <% birdIntake.forEach(function(record){ %>
      <div class="record-row">
        <p><%= record.batch_code %></p>
        <p><%= record.type %></p>
        <p><%= new Date(record.date_taken).toLocaleDateString() %></p>
        <p><%= record.no_of_bird %></p>
        <p><%= record.pen %></p>
        <p><%= record.supplier %></p>
        <p><%= record.price %></p>
        <p><%= record.cost %></p>
        <p><%= record.level %></p>
        <p><%= record.attendant_name %></p>
        <a href="/deleterecord/birdintake/<%= record._id %>"
          ><i class="material-icons" style="font-size: 32px; color: red"
            >delete</i
          ></a
        >
      </div>
      <% }) %> <% }else{ %>
      <p style="text-align: center">no record found</p>
      <% } %>
    </div>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Attach confirmation to all delete links
        document.querySelectorAll(".record-row a").forEach(function (link) {
          link.addEventListener("click", function (e) {
            const confirmed = confirm(
              "Are you sure you want to delete this bird intake record? This action cannot be undone."
            );
            if (!confirmed) {
              e.preventDefault();
            }
          });
        });
      });
    </script>
    <style>
      .page-header {
        margin: 40px 20px;
      }
      .record-container {
        margin: 30px auto;
        max-width: 1100px;
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        padding: 0 0 20px 0;
        overflow-x: auto;
      }
      .record-header,
      .record-row {
        display: grid;
        grid-template-columns: repeat(11, 1fr);
        align-items: center;
        gap: 8px;
        padding: 12px 16px;
      }
      .record-header {
        background: #f5f7fa;
        font-weight: 600;
        border-bottom: 2px solid #e0e0e0;
        color: #1565c0;
        font-size: 1rem;
      }
      .record-row {
        border-bottom: 1px solid #e0e0e0;
        transition: background 0.2s;
        font-size: 0.98rem;
      }
      .record-row:hover {
        background: #f0f4fa;
      }
      .record-row p,
      .record-header p {
        margin: 0;
        padding: 0 4px;
        word-break: break-word;
        text-align: center;
      }
      .record-row a {
        display: flex;
        justify-content: center;
        align-items: center;
        text-decoration: none;
      }
      @media (max-width: 900px) {
        .record-header,
        .record-row {
          font-size: 0.92rem;
          padding: 8px 4px;
        }
      }
      @media (max-width: 600px) {
        .record-header,
        .record-row {
          grid-template-columns: repeat(11, minmax(80px, 1fr));
          font-size: 0.85rem;
        }
        .record-container {
          padding: 0 0 10px 0;
        }
      }
    </style>
  </body>
</html>
