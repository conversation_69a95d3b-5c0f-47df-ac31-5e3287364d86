<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/stylesheets/global-styles.css" />
    <title>Create Supplier Account - Farm Management System</title>
  </head>
  <body>
    <!-- Modern Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="app-logo">Farm Management System</div>
        <div class="user-info">
          <span>Create Supplier Account</span>
        </div>
      </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav">
      <div class="breadcrumb-content">
        <a href="/management/homepage" class="breadcrumb-item">Dashboard</a>
        <span class="breadcrumb-separator">›</span>
        <a href="/management/homepage" class="breadcrumb-item">Management</a>
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-item active">Create Supplier</span>
      </div>
    </nav>

    <!-- Main Container -->
    <div class="app-container">
      <!-- Alerts -->
      <% if(hasErr){%>
      <div class="alert alert-danger">
        <div>
          <% errMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%> <% if(hasSuccess){%>
      <div class="alert alert-success">
        <div>
          <% successMsg.forEach(message =>{ %>
          <p class="mb-0"><%= message%></p>
          <% })%>
        </div>
      </div>
      <%}%>

      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">🏭 Create Supplier Account</h1>
        <p class="page-subtitle">
          Add new supplier to your farm management system
        </p>
        <div class="page-actions">
          <a href="/management/homepage" class="btn btn-secondary btn-back"
            >Back to Dashboard</a
          >
          <a href="/management/check-suppliers" class="btn btn-outline"
            >View All Suppliers</a
          >
        </div>
      </div>

      <!-- Supplier Form -->
      <div class="form-container">
        <form
          action="/management/createsupplier"
          method="POST"
          id="supplierForm"
        >
          <!-- Supplier Identification -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">🏷️ Supplier Identification</h3>
            </div>
            <div class="card-body">
              <div class="form-group">
                <label for="supplier_id" class="form-label"
                  >🏷️ Supplier ID</label
                >
                <select
                  name="supplier_id"
                  class="form-control form-select"
                  id="supplier_id"
                  required
                >
                  <option value="">Select supplier ID...</option>
                  <option value="CHI">CHI - Chi Farms Limited</option>
                  <option value="OBA">OBA - Oba Poultry Farms</option>
                  <option value="ANC">ANC - Anchor Farms Nigeria</option>
                  <option value="FSI">FSI - Farm Solutions Inc</option>
                </select>
              </div>
            </div>
          </div>
          <!-- Supplier Information -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">🏭 Supplier Information</h3>
            </div>
            <div class="card-body">
              <div class="form-grid">
                <div class="form-group">
                  <label for="name" class="form-label">🏭 Supplier Name</label>
                  <input
                    type="text"
                    class="form-control"
                    name="name"
                    id="name"
                    placeholder="Enter supplier company name"
                    required
                  />
                </div>

                <div class="form-group" style="grid-column: 1 / -1">
                  <label for="address" class="form-label"
                    >📍 Supplier Address</label
                  >
                  <textarea
                    class="form-control"
                    name="address"
                    id="address"
                    placeholder="Enter supplier complete address"
                    rows="3"
                    required
                  ></textarea>
                </div>
              </div>
            </div>
          </div>

          <!-- Financial Information -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">💰 Financial Information</h3>
            </div>
            <div class="card-body">
              <div class="form-grid">
                <div class="form-group">
                  <label for="account_balance" class="form-label"
                    >💰 Initial Account Balance (₦)</label
                  >
                  <input
                    type="number"
                    class="form-control"
                    name="account_balance"
                    id="account_balance"
                    placeholder="Enter initial balance"
                    min="0"
                    step="0.01"
                    value="0"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="account_no" class="form-label"
                    >🏦 Account Number</label
                  >
                  <input
                    type="text"
                    class="form-control"
                    name="account_no"
                    id="account_no"
                    placeholder="Enter bank account number"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="bank" class="form-label">🏛️ Bank Name</label>
                  <input
                    type="text"
                    class="form-control"
                    name="bank"
                    id="bank"
                    placeholder="Enter bank name"
                    required
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Supplier Summary -->
          <div class="card mb-4">
            <div class="card-header">
              <h3 class="card-title">📊 Supplier Summary</h3>
            </div>
            <div class="card-body">
              <div class="grid grid-4">
                <div class="text-center">
                  <h4 class="text-primary" id="supplierID">-</h4>
                  <p class="text-muted">Supplier ID</p>
                </div>
                <div class="text-center">
                  <h4 class="text-info" id="supplierName">-</h4>
                  <p class="text-muted">Company Name</p>
                </div>
                <div class="text-center">
                  <h4 class="text-success" id="initialBalance">₦0.00</h4>
                  <p class="text-muted">Initial Balance</p>
                </div>
                <div class="text-center">
                  <h4 class="text-warning" id="bankName">-</h4>
                  <p class="text-muted">Bank</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="d-flex justify-between align-center">
            <div>
              <span class="text-muted">All fields are required</span>
            </div>
            <div class="d-flex gap-2">
              <button type="reset" class="btn btn-secondary">
                🔄 Reset Form
              </button>
              <button type="submit" class="btn btn-success btn-save">
                🏭 Create Supplier
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Enhanced JavaScript -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const supplierIdSelect = document.getElementById("supplier_id");
        const nameInput = document.getElementById("name");
        const balanceInput = document.getElementById("account_balance");
        const bankInput = document.getElementById("bank");

        const supplierIDDisplay = document.getElementById("supplierID");
        const supplierNameDisplay = document.getElementById("supplierName");
        const initialBalanceDisplay = document.getElementById("initialBalance");
        const bankNameDisplay = document.getElementById("bankName");

        function updateDisplays() {
          const supplierId = supplierIdSelect.value || "-";
          const name = nameInput.value || "-";
          const balance = parseFloat(balanceInput.value) || 0;
          const bank = bankInput.value || "-";

          supplierIDDisplay.textContent = supplierId;
          supplierNameDisplay.textContent = name;
          initialBalanceDisplay.textContent = new Intl.NumberFormat("en-NG", {
            style: "currency",
            currency: "NGN",
          }).format(balance);
          bankNameDisplay.textContent = bank;
        }

        // Add event listeners
        [supplierIdSelect, nameInput, balanceInput, bankInput].forEach(
          (input) => {
            input.addEventListener("input", updateDisplays);
            input.addEventListener("change", updateDisplays);
          }
        );

        // Form submission enhancement
        const form = document.getElementById("supplierForm");
        form.addEventListener("submit", function (e) {
          const submitBtn = form.querySelector('button[type="submit"]');
          submitBtn.innerHTML = '<span class="loading"></span> Creating...';
          submitBtn.disabled = true;
        });

        // Initialize displays
        updateDisplays();
      });
    </script>
  </body>
</html>
